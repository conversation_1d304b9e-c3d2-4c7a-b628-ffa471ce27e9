package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.consts.DefaultWandsParamConst;
import com.mira.api.bluetooth.enums.TWarningCodeEnum;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.core.consts.enums.WandTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * T线 warning 校验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TLineCheckHelper {
    /**
     * T线 warning 校验
     *
     * @return T线校验有问题的数据，Ecode设置为B03，return false
     */
    public boolean tLineWarningCheck(Long userId, HormoneDTO hormoneDTO,
                                     AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO) {
        String uStripType = dataUploadEntity.getTestWandType().substring(1);
        String t1WarningCode = dataUploadEntity.getT1WarningCode();
        String t2WarningCode = dataUploadEntity.getT2WarningCode();

        Float lhLowerLimit = DefaultWandsParamConst.LH_LOWER_LIMIT;
        Float lhUpperLimit = DefaultWandsParamConst.LH_UPPER_LIMIT;
        Float e3gLowerLimit = DefaultWandsParamConst.E3G_LOWER_LIMIT;
        Float e3gUpperLimit = DefaultWandsParamConst.E3G_UPPER_LIMIT;

        if (Objects.nonNull(wandsParamRecordDTO)) {
            if (uStripType.equals(WandTypeEnum.LH.getString())) {
                lhLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                lhUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
            } else {
                // E3G+LH
                lhLowerLimit = wandsParamRecordDTO.getFLowerLimit3();
                lhUpperLimit = wandsParamRecordDTO.getFUpperLimit3();
                e3gLowerLimit = wandsParamRecordDTO.getFLowerLimit2();
                e3gUpperLimit = wandsParamRecordDTO.getFUpperLimit2();
            }
        }

        /*
         * T1warningcode=0x00   LH : T1_con
         * T1warningcode=0x01   LH >T1_con
         * T1warningcode=0x02   LH >T1_con
         * T1warningcode=0x03   LH <T1_con
         * T1warningcode=0x04   LH <T1_con
         */
        if (uStripType.equals(WandTypeEnum.LH.getString())) {
            if (TWarningCodeEnum.CODE_0.getValue().equals(t1WarningCode)) {
                return true;
            }
            HormoneDTO.TestResult test_results = hormoneDTO.getTest_results();
            if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_2.getValue().equals(t1WarningCode)) {
                test_results.setValue1(lhUpperLimit);
            } else if (TWarningCodeEnum.CODE_3.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                test_results.setValue1(lhLowerLimit);
            }
            hormoneDTO.setTest_results(test_results);
            log.info("userId:{} LH试纸,tLineWarningCheck 01", userId);
            return false;

        } else {
            // E3G+LH
            /*
             * T1warningcode=0x00    E3G : T1_con
             * T1warningcode=0x01    E3G <T1_con
             * T1warningcode=0x02    E3G >T1_con
             * T1warningcode=0x03    E3G <T1_con
             * T1warningcode=0x04    E3G >T1_con
             *
             * T2warningcode=0x00    LH : T2_con
             * T2warningcode=0x01    LH >T2_con
             * T2warningcode=0x02    LH >T2_con
             * T2warningcode=0x03    LH <T2_con
             * T2warningcode=0x04    LH <T2_con
             */
            if (TWarningCodeEnum.CODE_0.getValue().equals(t1WarningCode) && TWarningCodeEnum.CODE_0.getValue().equals(t2WarningCode)) {
                return true;
            }
            HormoneDTO.TestResult test_results = hormoneDTO.getTest_results();
            if (TWarningCodeEnum.CODE_1.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_3.getValue().equals(t1WarningCode)) {
                test_results.setValue1(e3gLowerLimit);
            } else if (TWarningCodeEnum.CODE_2.getValue().equals(t1WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t1WarningCode)) {
                test_results.setValue1(e3gUpperLimit);
            }
            if (TWarningCodeEnum.CODE_1.getValue().equals(t2WarningCode) || TWarningCodeEnum.CODE_2.getValue().equals(t2WarningCode)) {
                test_results.setValue2(lhUpperLimit);
            } else if (TWarningCodeEnum.CODE_3.getValue().equals(t2WarningCode) || TWarningCodeEnum.CODE_4.getValue().equals(t2WarningCode)) {
                test_results.setValue2(lhLowerLimit);
            }
            hormoneDTO.setTest_results(test_results);
            log.info("userId:{} E3G+LH试纸, tLineWarningCheck 02", userId);
            return false;
        }
    }
}
