package com.mira.bluetooth.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.PregnantRiskDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmLongerPeriodDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.dto.report.AlgorithmReportDataDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.tips.AlgorithmGetTipsDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.LastCycleFlagEnum;
import com.mira.api.bluetooth.enums.ThresholdModeEnum;
import com.mira.api.user.dto.user.diary.DateSymptomDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import com.mira.bluetooth.client.AlgorithmClient;
import com.mira.bluetooth.client.AlgorithmResult;
import com.mira.bluetooth.dal.dao.AppUserAlgorithmResultDAO;
import com.mira.bluetooth.dal.dao.AppUserMenopauseResultDAO;
import com.mira.bluetooth.dal.entity.AppUserAlgorithmResultEntity;
import com.mira.bluetooth.dal.entity.AppUserMenopauseResultEntity;
import com.mira.bluetooth.dto.algorithm.request.*;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmMenopauseResultDTO;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.dto.algorithm.response.LongerPeriodReturnDTO;
import com.mira.bluetooth.enums.FeignCodeEnum;
import com.mira.bluetooth.exception.BluetoothException;
import com.mira.bluetooth.handler.event.editperiod.EditPeriodEvent;
import com.mira.bluetooth.handler.event.requestlog.AlgorithmLogEvent;
import com.mira.bluetooth.properties.BluetoothProperties;
import com.mira.bluetooth.service.util.UserModeHandler;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.SpringContextHolder;
import com.mira.core.util.ZoneDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 算法调用层
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlgorithmInvokeManager {
    @Resource
    private AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;
    @Resource
    private UserModeHandler userModeHandler;
    @Resource
    private AlgorithmClient algorithmClient;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private AppUserMenopauseResultDAO userMenopauseResultDAO;
    @Resource
    private BluetoothProperties bluetoothProperties;

    /**
     * 构建新数据请求体
     */
    public NewHormoneDataRequest buildNewHormoneRequest(Long userId, String today, Integer trialFlag,
                                                        List<HormoneDTO> hormoneDTOList) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 构建请求数据
        NewHormoneDataRequest newHormoneDataRequest = new NewHormoneDataRequest();
        newHormoneDataRequest.setUser_id(userId);
        newHormoneDataRequest.setToday(today);
        newHormoneDataRequest.setTimeZone(timeZone);

        AppUserAlgorithmResultEntity userAlgorithmResult = appUserAlgorithmResultDAO.getByUserId(userId);
        newHormoneDataRequest.setThreshold_mode(userAlgorithmResult.getThresholdMode());
        newHormoneDataRequest.setCycle_data(JsonUtil.toArray(userAlgorithmResult.getCycleData(), CycleDataDTO.class));
        List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userAlgorithmResult.getHormoneData(), HormoneDTO.class);
        newHormoneDataRequest.setHormone_data_history(filterHormoneData(hormoneDTOS));

        userModeHandler.process(newHormoneDataRequest, trialFlag);
        newHormoneDataRequest.setHormone_data_new(filterHormoneData(hormoneDTOList));

        return newHormoneDataRequest;
    }

    /**
     * 调用新数据
     */
    public AlgorithmReturnDTO callNewHormoneData(NewHormoneDataRequest newHormoneDataRequest) {
        AlgorithmResult<AlgorithmReturnDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().sendNewHormoneData(newHormoneDataRequest);
        } catch (Exception e) {
            log.error("user:{}, call new hormone error", newHormoneDataRequest.getUser_id(), e);
            algorithmResult = null;
        }

        if (algorithmResult == null) {
            // save fail log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                    newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, AlgorithmRequestTypeEnum.NEW_HORMONE_DATA, "call new hormone error"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                    newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, AlgorithmRequestTypeEnum.NEW_HORMONE_DATA, algorithmResult.getErrorMessage()));
            log.error("user:{}, call new hormone return fail:{}", newHormoneDataRequest.getUser_id(), algorithmResult.getErrorMessage());
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, AlgorithmRequestTypeEnum.NEW_HORMONE_DATA));

        return algorithmResult.getData();
    }

    /**
     * 构建编辑经期请求体
     */
    public EditPeriodRequest buildEditPeriodRequest(AlgorithmEditPeriodDTO algorithmEditPeriodDTO,
                                                    List<HormoneDTO> hormoneDTOS) {
        String timeZone = algorithmEditPeriodDTO.getTimeZone();
        Integer trialFlag = algorithmEditPeriodDTO.getTrialFlag();
        Integer lastCycleFlag = algorithmEditPeriodDTO.getLastCycleFlag();
        UserPeriodParamDTO userPeriodParamDTO = algorithmEditPeriodDTO.getUserPeriodParam();
        List<UserPeriodDataDTO> userPeriodDataDTOS = algorithmEditPeriodDTO.getUserPeriodDataList();
        AlgorithmRequestTypeEnum algorithmRequestTypeEnum = algorithmEditPeriodDTO.getAlgorithmRequestTypeEnum();

        // edit period rquest
        EditPeriodRequest editPeriodRequest = new EditPeriodRequest();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        editPeriodRequest.setUser_id(userPeriodParamDTO.getUserId());
        editPeriodRequest.setToday(today);
        editPeriodRequest.setTimeZone(timeZone);
        editPeriodRequest.setPregnant_info_list(algorithmEditPeriodDTO.getPregnantInfoList());

        // set hormone_data
        editPeriodRequest.setHormone_data(hormoneDTOS);
        // set user_type、len_cycle_0
        userModeHandler.process(editPeriodRequest, userPeriodParamDTO, trialFlag);
        // algorithm result
        AppUserAlgorithmResultEntity userAlgorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userPeriodParamDTO.getUserId());

        if (LastCycleFlagEnum.TWO.getFlag().equals(lastCycleFlag)) {
            handleExtendPeriodFlagParam(userPeriodDataDTOS, lastCycleFlag, userAlgorithmResultEntity);
        } else {
            handleEditPeriodFlagParam(userPeriodDataDTOS, lastCycleFlag, algorithmRequestTypeEnum, userAlgorithmResultEntity);
        }
        List<UserPeriodDataDTO> extend_period = userPeriodParamDTO.getExtend_period();
        if (CollectionUtils.isNotEmpty(extend_period)) {
            editPeriodRequest.setExtend_period(extend_period);
        } else {
            editPeriodRequest.setExtend_period(new ArrayList<>());
        }
        // set period_data
        editPeriodRequest.setPeriod_data(userPeriodDataDTOS);
        // set threshold_mode
        if (AlgorithmRequestTypeEnum.CHANGE_THRESHOLD_MODE.equals(algorithmRequestTypeEnum)) {
            editPeriodRequest.setThreshold_mode(ThresholdModeEnum.LOW_LH.getCode());
        } else {
            if (userAlgorithmResultEntity == null) {
                editPeriodRequest.setThreshold_mode(ThresholdModeEnum.NORMAL.getCode());
            } else {
                editPeriodRequest.setThreshold_mode(userAlgorithmResultEntity.getThresholdMode());
            }
        }

        return editPeriodRequest;
    }

    /**
     * 调用编辑经期
     */
    public AlgorithmReturnDTO callEditPeriod(EditPeriodRequest editPeriodRequest, AlgorithmRequestTypeEnum algorithmRequestTypeEnum,
                                             boolean extraEvent) {
        AlgorithmResult<AlgorithmReturnDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().sendEditPeriodData(editPeriodRequest);
        } catch (Exception e) {
            log.error("user:{}, call edit period error", editPeriodRequest.getUser_id(), e);
            algorithmResult = null;
        }

        if (algorithmResult == null) {
            // save fail log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(editPeriodRequest.getUser_id(),
                    editPeriodRequest.getTimeZone(), editPeriodRequest, algorithmRequestTypeEnum, "call edit period error"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(editPeriodRequest.getUser_id(),
                    editPeriodRequest.getTimeZone(), editPeriodRequest, algorithmRequestTypeEnum, algorithmResult.getErrorMessage()));
            log.error("user:{}, call edit period return fail:{}", editPeriodRequest.getUser_id(), algorithmResult.getErrorMessage());
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(editPeriodRequest.getUser_id(),
                editPeriodRequest.getTimeZone(), editPeriodRequest, algorithmRequestTypeEnum));

        if (extraEvent) {
            // 编辑经期拿到算法返回结果的事件
            SpringContextHolder.publishEvent(new EditPeriodEvent(editPeriodRequest.getUser_id(),
                    editPeriodRequest.getTimeZone(), algorithmResult.getData()));
        }

        return algorithmResult.getData();
    }

    /**
     * 构建长周期请求体
     */
    public LongerPeriodRequest buildLongerPeriodRequest(AlgorithmLongerPeriodDTO algorithmLongerPeriodDTO,
                                                        List<CycleDataDTO> cycleDataDTOS) {
        Long userId = algorithmLongerPeriodDTO.getUserId();
        String timeZone = algorithmLongerPeriodDTO.getTimeZone();
        Integer userMode = algorithmLongerPeriodDTO.getUserMode();
        Integer cycleFlag = algorithmLongerPeriodDTO.getCycleFlag();
        Integer periodFlag = algorithmLongerPeriodDTO.getPeriodFlag();

        LongerPeriodRequest longerPeriodRequest = new LongerPeriodRequest();
        longerPeriodRequest.setUser_id(userId);
        longerPeriodRequest.setTimeZone(timeZone);
        longerPeriodRequest.setCycle_data(cycleDataDTOS);
        userModeHandler.process(longerPeriodRequest, cycleFlag, periodFlag, userMode);

        return longerPeriodRequest;
    }

    /**
     * 调用长周期
     */
    public LongerPeriodReturnDTO callLongerPeriod(LongerPeriodRequest longerPeriodRequest) {
        AlgorithmResult<LongerPeriodReturnDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().sendLongerEditData(longerPeriodRequest);
        } catch (Exception e) {
            log.error("user:{}, call longer period error", longerPeriodRequest.getUser_id(), e);
            algorithmResult = null;
        }

        if (algorithmResult == null) {
            // save fail log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(longerPeriodRequest.getUser_id(),
                    longerPeriodRequest.getTimeZone(), longerPeriodRequest, AlgorithmRequestTypeEnum.LONGER_PERIOD_EDIT, "call longer period error"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            log.error("user:{}, call longer period return fail:{}", longerPeriodRequest.getUser_id(), algorithmResult.getErrorMessage());
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(longerPeriodRequest.getUser_id(),
                    longerPeriodRequest.getTimeZone(), longerPeriodRequest, AlgorithmRequestTypeEnum.LONGER_PERIOD_EDIT, algorithmResult.getErrorMessage()));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(longerPeriodRequest.getUser_id(),
                longerPeriodRequest.getTimeZone(), longerPeriodRequest, AlgorithmRequestTypeEnum.LONGER_PERIOD_EDIT));

        return algorithmResult.getData();
    }

    /**
     * 构建tips请求体
     */
    public TipsRequest buildTipsRequest(AlgorithmGetTipsDTO algorithmGetTipsDTO) {
        String timeZone = algorithmGetTipsDTO.getTimeZone();
        Integer trialFlag = algorithmGetTipsDTO.getTrialFlag();
        UserPeriodParamDTO userPeriodParamDTO = algorithmGetTipsDTO.getUserPeriodParam();
        Long userId = userPeriodParamDTO.getUserId();

        TipsRequest tipsRequest = new TipsRequest();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        tipsRequest.setUser_id(userId);
        tipsRequest.setToday(today);
        tipsRequest.setTimeZone(timeZone);
        tipsRequest.setRemind_flag(algorithmGetTipsDTO.getRemindFlag());
        tipsRequest.setTesting_schedule_flag(algorithmGetTipsDTO.getTestingScheduleFlag());
        MenopauseResultDTO menopauseResultDTO = cacheManager.getMenopauseResultCache(userId);
        if (menopauseResultDTO == null) {
            AppUserMenopauseResultEntity userMenopauseResultEntity = userMenopauseResultDAO.getByUserId(userId);
            if (userMenopauseResultEntity != null) {
                menopauseResultDTO = BeanUtil.toBean(userMenopauseResultEntity, MenopauseResultDTO.class);
            }
        }
        if (menopauseResultDTO != null) {
            tipsRequest.setTracking_menopause_define_stage(menopauseResultDTO.getDefineStage());
            tipsRequest.setTracking_menopause_backend_status(menopauseResultDTO.getBackendStatus());
            tipsRequest.setTracking_menopause_define_stage_date(menopauseResultDTO.getDefineStageDate());
            tipsRequest.setTracking_menopause_progress_status(menopauseResultDTO.getProgressStatus());
        }

        AppUserAlgorithmResultEntity userAlgorithmResultEntity = appUserAlgorithmResultDAO.getByUserId(userId);
        tipsRequest.setBarTip(userAlgorithmResultEntity.getBarTip());
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userAlgorithmResultEntity.getCycleData(), CycleDataDTO.class);
        setCycleDataDefault(cycleDataDTOS);
        tipsRequest.setCycle_data(cycleDataDTOS);

        List<HormoneDTO> historyHormoneDTOS = JsonUtil.toArray(userAlgorithmResultEntity.getHormoneData(), HormoneDTO.class);
        tipsRequest.setHormone_data(filterHormoneData(historyHormoneDTOS));

        // bbt
        if (1 == algorithmGetTipsDTO.getTipsType()) {
            tipsRequest.setBind_bbt(algorithmGetTipsDTO.getBindBbt());
            tipsRequest.setBbt(algorithmGetTipsDTO.getBbt());
        }

        // set user_type、user_mode、age、user_conditions、len_cycle_0、
        userModeHandler.process(tipsRequest, userPeriodParamDTO, trialFlag);
        // tips type
        tipsRequest.setTest_mode(algorithmGetTipsDTO.getTipsType());

        return tipsRequest;
    }

    /**
     * 调用tips
     */
    public GetTipsReturnDTO callTips(TipsRequest tipsRequest) {
        AlgorithmResult<GetTipsReturnDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().sendTipsData(tipsRequest);
        } catch (Exception e) {
            log.error("user:{}, call tips error", tipsRequest.getUser_id(), e);
            algorithmResult = null;
        }

        if (algorithmResult == null) {
            // save fail log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(tipsRequest.getUser_id(),
                    tipsRequest.getTimeZone(), tipsRequest, AlgorithmRequestTypeEnum.GET_TIPS, "call tips error"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            log.error("user:{}, call tips return fail:{}", tipsRequest.getUser_id(), algorithmResult.getErrorMessage());
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(tipsRequest.getUser_id(),
                    tipsRequest.getTimeZone(), tipsRequest, AlgorithmRequestTypeEnum.GET_TIPS, algorithmResult.getErrorMessage()));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(tipsRequest.getUser_id(),
                tipsRequest.getTimeZone(), tipsRequest, AlgorithmRequestTypeEnum.GET_TIPS));

        return algorithmResult.getData();
    }

    /**
     * 构建report请求体
     */
    public ReportRequest buildReportRequest(AlgorithmReportDataDTO algorithmReportDataDTO) {
        String timeZone = algorithmReportDataDTO.getTimeZone();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        UserPeriodParamDTO userPeriodParam = algorithmReportDataDTO.getUserPeriodParam();

        ReportRequest reportRequest = new ReportRequest();
        reportRequest.setUser_id(userPeriodParam.getUserId());
        reportRequest.setToday(today);
        reportRequest.setTimeZone(timeZone);
        reportRequest.setCycle_data(algorithmReportDataDTO.getCycleDataDTOS());
        reportRequest.setHormone_data(filterHormoneData(algorithmReportDataDTO.getHormoneDataDTOS()));

        // set user_type、user_mode、age、user_conditions、len_cycle_0、
        userModeHandler.process(reportRequest, algorithmReportDataDTO.getUserPeriodParam());

        List<DateSymptomDTO> selectUserSymptomDTOS = JsonUtil.toArray(algorithmReportDataDTO.getListDateSymptomDTO(), DateSymptomDTO.class);
        List<ReportRequest.SymptomDTO> symptoms = new ArrayList<>();
        for (DateSymptomDTO dateSymptomDTO : selectUserSymptomDTOS) {
            String date = dateSymptomDTO.getDate();
            List<UserSymptomDTO> symptomDTOS = dateSymptomDTO.getSymptomDTOS();
            for (UserSymptomDTO appUserSymptomDTO : symptomDTOS) {
                ReportRequest.SymptomDTO symptomDTO = new ReportRequest.SymptomDTO();
                symptomDTO.setDate(date);
                symptomDTO.setType(appUserSymptomDTO.getValue());
                symptomDTO.setLevel(appUserSymptomDTO.getLevel());
                symptoms.add(symptomDTO);
            }
        }
        reportRequest.setSymptoms(symptoms);

        return reportRequest;
    }

    /**
     * 调用report
     */
    public ReportReturnDTO callReport(ReportRequest reportRequest) {
        AlgorithmResult<ReportReturnDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().sendReportData(reportRequest);
        } catch (Exception e) {
            log.error("user:{}, call report error", reportRequest.getUser_id(), e);
            algorithmResult = null;
        }

        if (algorithmResult == null) {
            // save request log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(reportRequest.getUser_id(),
                    reportRequest.getTimeZone(), reportRequest, AlgorithmRequestTypeEnum.MIRA_REPORT, "call report error"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            log.error("user:{}, call report return fail:{}", reportRequest.getUser_id(), algorithmResult.getErrorMessage());
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(reportRequest.getUser_id(),
                    reportRequest.getTimeZone(), reportRequest, AlgorithmRequestTypeEnum.MIRA_REPORT, algorithmResult.getErrorMessage()));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(reportRequest.getUser_id(),
                reportRequest.getTimeZone(), reportRequest, AlgorithmRequestTypeEnum.MIRA_REPORT));

        return algorithmResult.getData();
    }

    /**
     * 筛选测试数据 (过滤掉xxx年之前的数据，因为这种数据肯定是异常数据)
     */
    public List<HormoneDTO> filterHormoneData(List<HormoneDTO> hormoneDTOS) {
        return hormoneDTOS.stream()
                          .filter(hormone -> StringUtils.isNotBlank(hormone.getTest_time())
                                  && Integer.parseInt(hormone.getTest_time().substring(0, 4)) >= bluetoothProperties.getHormineFilterYear())
                          .collect(Collectors.toList());
    }

    private void handleExtendPeriodFlagParam(List<UserPeriodDataDTO> userPeriodDataDTOS, Integer lastCycleFlag,
                                             AppUserAlgorithmResultEntity userAlgorithmResultEntity) {
        if (CollectionUtils.isEmpty(userPeriodDataDTOS)) {
            return;
        }

        UserPeriodDataDTO lastUserPeriodDataDTO = userPeriodDataDTOS.get(userPeriodDataDTOS.size() - 1);
        lastUserPeriodDataDTO.setFlag(lastCycleFlag);
        if (ObjectUtils.isEmpty(userAlgorithmResultEntity)) {
            lastUserPeriodDataDTO.setFlag(LastCycleFlagEnum.ZERO.getFlag());
            return;
        }

        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userAlgorithmResultEntity.getCycleData(), CycleDataDTO.class);
        List<CycleDataDTO> userRealCycleDataDTOS = cycleDataDTOS.stream()
                                                                .filter(cycleDataDTO -> cycleDataDTO.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus())
                                                                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userRealCycleDataDTOS)) {
            CycleDataDTO lastUserRealCycleDataDTO = userRealCycleDataDTOS.get(userRealCycleDataDTOS.size() - 1);
            // set last real cycle length
            lastUserPeriodDataDTO.setLen_cycle(lastUserRealCycleDataDTO.getLen_cycle());
        }
    }

    private void handleEditPeriodFlagParam(List<UserPeriodDataDTO> userPeriodDataDTOS, Integer lastCycleFlag, AlgorithmRequestTypeEnum algorithmRequestTypeEnum,
                                           AppUserAlgorithmResultEntity userAlgorithmResultEntity) {
        if (CollectionUtils.isEmpty(userPeriodDataDTOS)) {
            return;
        }
        UserPeriodDataDTO lastUserPeriodDataDTO = userPeriodDataDTOS.get(userPeriodDataDTOS.size() - 1);
        lastUserPeriodDataDTO.setFlag(lastCycleFlag);
        if (ObjectUtils.isEmpty(userAlgorithmResultEntity)) {
            return;
        }

        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userAlgorithmResultEntity.getCycleData(), CycleDataDTO.class);
        List<CycleDataDTO> userRealCycleDataDTOS = cycleDataDTOS.stream()
                                                                .filter(cycleDataDTO -> cycleDataDTO.getCycle_status() == CycleStatusEnum.REAL_CYCLE.getStatus())
                                                                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userRealCycleDataDTOS)) {
            return;
        }

        CycleDataDTO lastUserRealCycleDataDTO = userRealCycleDataDTOS.get(userRealCycleDataDTOS.size() - 1);
        // set last real cycle length
        lastUserPeriodDataDTO.setLen_cycle(lastUserRealCycleDataDTO.getLen_cycle());

        if (!AlgorithmRequestTypeEnum.EDIT_CYCLE_LENGTH.equals(algorithmRequestTypeEnum)) {
            if (lastCycleFlag == LastCycleFlagEnum.ZERO.getFlag()
                    && lastUserRealCycleDataDTO.getDate_period_start().equals(lastUserPeriodDataDTO.getDate_period_start())) {
                lastUserPeriodDataDTO.setFlag(LastCycleFlagEnum.ONE.getFlag());
            }
        }
        if (lastUserPeriodDataDTO.getLen_cycle() == null) {
            lastUserPeriodDataDTO.setFlag(LastCycleFlagEnum.ZERO.getFlag());
        }
    }

    private void setCycleDataDefault(List<CycleDataDTO> cycleDataDTOS) {
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (cycleDataDTO.getDate_PDG_rise() == null) {
                cycleDataDTO.setDate_PDG_rise(new ArrayList<>());
            }
            if (cycleDataDTO.getCycle_cd_index() == null) {
                cycleDataDTO.setCycle_cd_index(new ArrayList<>());
            }
            if (cycleDataDTO.getPregnant_risk() == null) {
                cycleDataDTO.setPregnant_risk(new PregnantRiskDTO());
            }
            if (cycleDataDTO.getFertility_score_list() == null) {
                cycleDataDTO.setFertility_score_list(new ArrayList<>());
            }
            if (cycleDataDTO.getTesting_day_list() == null) {
                cycleDataDTO.setTesting_day_list(new TestingProductDayDTO());
            }
        }
    }

    /**
     * 构建新数据请求体
     */
    public NewHormoneDataRequest buildGetMenopauseResultRequest(Long userId) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // 构建请求数据
        NewHormoneDataRequest newHormoneDataRequest = new NewHormoneDataRequest();
        newHormoneDataRequest.setUser_id(userId);
        newHormoneDataRequest.setToday(today);
        newHormoneDataRequest.setTimeZone(timeZone);

        AppUserAlgorithmResultEntity userAlgorithmResult = appUserAlgorithmResultDAO.getByUserId(userId);
        newHormoneDataRequest.setThreshold_mode(userAlgorithmResult.getThresholdMode());
        newHormoneDataRequest.setCycle_data(JsonUtil.toArray(userAlgorithmResult.getCycleData(), CycleDataDTO.class));
        List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userAlgorithmResult.getHormoneData(), HormoneDTO.class);
        newHormoneDataRequest.setHormone_data_history(filterHormoneData(hormoneDTOS));
        hormoneDTOS.removeIf(
                hormoneDTO -> !WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type())
        );

        userModeHandler.process(newHormoneDataRequest, null);
        newHormoneDataRequest.setHormone_data_new(new ArrayList<>());

        return newHormoneDataRequest;
    }

    public AlgorithmMenopauseResultDTO callGetMenopauseResult(NewHormoneDataRequest newHormoneDataRequest, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        AlgorithmResult<AlgorithmMenopauseResultDTO> algorithmResult;
        try {
            algorithmResult = algorithmClient.get().getMenopauseResult(newHormoneDataRequest);
        } catch (Exception e) {
            log.error("user:{}, algorithm error", newHormoneDataRequest.getUser_id(), e);
            algorithmResult = null;
        }


        if (algorithmResult == null) {
            log.error("The algorithm is taking a nap. Try again later!");
            // save fail log
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                    newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, algorithmRequestTypeEnum, "algorithm result is null"));
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }
        if (!algorithmResult.getCode().equals(HttpStatus.OK.value())) {
            SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                    newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, algorithmRequestTypeEnum, algorithmResult.getErrorMessage()));
            log.error("Our algorithm had a hiccup. Maybe your data's tricky? Contact us!:{}", algorithmResult.getErrorMessage());
            throw new BluetoothException(FeignCodeEnum.SERVER_ERROR);
        }

        // save success request log
        SpringContextHolder.publishEvent(new AlgorithmLogEvent(newHormoneDataRequest.getUser_id(),
                newHormoneDataRequest.getTimeZone(), newHormoneDataRequest, algorithmRequestTypeEnum));

        return algorithmResult.getData();
    }
}
