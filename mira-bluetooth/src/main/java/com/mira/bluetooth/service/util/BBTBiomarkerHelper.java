package com.mira.bluetooth.service.util;

import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.core.consts.enums.TempUnitEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-12-19
 **/
@Component
public class BBTBiomarkerHelper {
    @Resource
    private IUserProvider userProvider;

    public WandTestBiomarkerDTO buildBBTBiomarkerDTO(Long userId, TemperatureDTO temperatureDTO) {
        CustomLogConfigDTO userCustomLogConfigVO = userProvider.getCustomLogConfig(userId).getData();
        return buildBBTBiomarkerDTO(userId, temperatureDTO, userCustomLogConfigVO.getTempUnit());
    }

    public WandTestBiomarkerDTO buildBBTBiomarkerDTO(Long userId, TemperatureDTO temperatureDTO, String tempUnit) {
        String tempTime = temperatureDTO.getTempTime();
        BigDecimal tempCSource = temperatureDTO.getTempC();
        BigDecimal tempFSource = temperatureDTO.getTempF();
        String modeError = temperatureDTO.getEcode();

        WandTestBiomarkerDTO wandTestBiomarkerDTO = new WandTestBiomarkerDTO("BBT", tempTime, modeError);
        BigDecimal tempC = tempCSource.setScale(2, RoundingMode.HALF_UP);
        BigDecimal tempF = tempFSource.setScale(2, RoundingMode.HALF_UP);
        String testValue;

        if (TempUnitEnum.C.getValue().equals(tempUnit)) {
            testValue = tempC.floatValue() + "℃" + " (" + tempF.floatValue() + "℉)";
        } else {
            testValue = tempF.floatValue() + "℉" + " (" + tempC.floatValue() + "℃)";
        }
        wandTestBiomarkerDTO.setTestValue(testValue);
        return wandTestBiomarkerDTO;
    }
}
