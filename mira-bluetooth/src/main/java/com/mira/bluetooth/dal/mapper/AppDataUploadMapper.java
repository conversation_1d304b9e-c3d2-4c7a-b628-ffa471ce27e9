package com.mira.bluetooth.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * app_data_upload
 *
 * <AUTHOR>
 */
@Mapper
public interface AppDataUploadMapper extends BaseMapper<AppDataUploadEntity> {
    List<AppDataUploadEntity> pageWithDeleted(@Param("currIndex") int currIndex,
                                              @Param("pageSize") int pageSize,
                                              @Param("testWandType") String testWandType,
                                              @Param("userId") Long userId);

    Integer countWithDeleted(@Param("testWandType") String testWandType,
                             @Param("userId") Long userId);

    void changeDeleteStatus(@Param("id") Long id, @Param("deleted") Integer deleted,
                            @Param("sysNote") String sysNote, @Param("runBoardFlag") Integer runBoardFlag);

    AppDataUploadEntity getByIdWithDeleted(Long id);

    void changeCompleteTime(@Param("id") Long id, @Param("newCompleteTime") String newCompleteTime,
                            @Param("completeTimestamp") Long completeTimestamp, @Param("sysNote") String sysNote);

    List<Long> getCompleteTimestampByUserId(@Param("userId") Long userId);
}
