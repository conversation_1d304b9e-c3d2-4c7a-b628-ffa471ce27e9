package com.mira.bluetooth.handler.event.upload.execute;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.dto.StatisticsNotificationDTO;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.mongo.dto.WandsExceptionAlarmDTO;
import com.mira.api.mongo.enums.WandsExceptionAlarmEnum;
import com.mira.api.mongo.provider.IWandsExceptionAlarmProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.UserPaywallDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.async.EmailProducer;
import com.mira.bluetooth.async.PatientProducer;
import com.mira.bluetooth.dal.dao.AppDataUploadDAO;
import com.mira.bluetooth.dal.dao.SnDataLimitDAO;
import com.mira.bluetooth.dal.entity.SnDataLimitEntity;
import com.mira.bluetooth.dto.algorithm.request.NewHormoneDataRequest;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.handler.event.dto.NewHormoneEventDTO;
import com.mira.bluetooth.properties.MongoRefProperties;
import com.mira.bluetooth.service.IAlgorithmService;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.bluetooth.service.manager.CycleIrregularManager;
import com.mira.bluetooth.service.util.CheckWandsUtil;
import com.mira.bluetooth.service.util.NewHormoneNotificationCheckUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 处理新数据上传后的业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NewHormoneEventExecute {
    @Resource
    private AppDataUploadDAO appDataUploadDAO;
    @Resource
    private SnDataLimitDAO snDataLimitDAO;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IAlgorithmService algorithmService;

    @Resource
    private PatientProducer patientProducer;
    @Resource
    private EmailProducer emailProducer;

    @Resource
    private IUserProvider userProvider;
    @Resource
    private IMessageProvider messageProvider;
    @Resource
    private IWandsExceptionAlarmProvider wandsExceptionAlarmProvider;

    @Resource
    private SysDictProperties sysDictProperties;
    @Resource
    private MongoRefProperties mongoRefProperties;
    @Resource
    private CycleIrregularManager cycleIrregularManager;

    @Resource
    private RedisComponent redisComponent;

    /**
     * 处理新数据上传后的业务逻辑
     */
    public void process(NewHormoneEventDTO newHormoneEventDTO) {
        Long userId = newHormoneEventDTO.getUserId();
        NewHormoneDataRequest newHormoneDataRequest = newHormoneEventDTO.getNewHormoneDataRequest();
        AlgorithmReturnDTO algorithmReturnDTO = newHormoneEventDTO.getAlgorithmReturnData();

        // 处理测试后立马产生的通知：LH高值提醒通知、HCG测试结果通知、PDG首次测试通知
        CompletableFuture.runAsync(() -> checkNotification(userId, newHormoneDataRequest, algorithmReturnDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("checkNotification error，userId：{}", userId, ex);
                    return null;
                });
        // 处理LH高值复测
        CompletableFuture.runAsync(() -> checkLHTestAgainReminder(userId, newHormoneDataRequest, algorithmReturnDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("checkLHTestAgainReminder error，userId：{}", userId, ex);
                    return null;
                });
        // 处理病人相关业务
        CompletableFuture.runAsync(() -> processPatientBiz(userId, newHormoneDataRequest, algorithmReturnDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("processPatientBiz error，userId：{}", userId, ex);
                    return null;
                });
        // 处理试剂相关业务
//        CompletableFuture.runAsync(() -> processWandsBiz(userId, newHormoneDataRequest), ThreadPoolUtil.getPool())
//                .exceptionally(ex -> {
//                    log.error("processWandsBiz error，userId：{}", userId, ex);
//                    return null;
//                });
        // 处理sn limit相关业务
        CompletableFuture.runAsync(() -> processSnLimit(userId, newHormoneDataRequest, newHormoneEventDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("processSnLimit error，userId：{}", userId, ex);
                    return null;
                });
        // 处理mira desk异常告警
        CompletableFuture.runAsync(() -> verifyWandsExceptionAlarm(userId, newHormoneDataRequest), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("verifyExceptionAlarm error，userId：{}", userId, ex);
                    return null;
                });
        // 处理pay wall
        CompletableFuture.runAsync(() -> processPayWall(userId, newHormoneDataRequest, algorithmReturnDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("processPayWall error，userId：{}", userId, ex);
                    return null;
                });
        // 处理周期是否规则的校验
        CompletableFuture.runAsync(() -> cycleIrregularManager.execuse(userId, newHormoneDataRequest.getTimeZone(), algorithmReturnDTO), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("checkIrregularCycles error，userId：{}", userId, ex);
                    return null;
                });
        // 处理notification statistics
        CompletableFuture.runAsync(() -> processTestingReminder(userId, newHormoneDataRequest), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("processTestingReminder，userId：{}", userId, ex);
                    return null;
                });
        // 标记完成一次测试
        CompletableFuture.runAsync(() -> markUserTestCompleted(userId), ThreadPoolUtil.getPool())
                .exceptionally(ex -> {
                    log.error("markUserTestCompleted，userId{}", userId, ex);
                    return null;
                });
    }


    /**
     * 数据上传后是否立马推送的校验
     */
    private void checkNotification(Long userId, NewHormoneDataRequest newHormoneDataRequest, AlgorithmReturnDTO algorithmReturnDTO) {
        String today = newHormoneDataRequest.getToday();
        String timeZone = newHormoneDataRequest.getTimeZone();
        List<HormoneDTO> hormone_data_new = newHormoneDataRequest.getHormone_data_new();
        List<CycleDataDTO> cycleDataDTOS = algorithmReturnDTO.getCycle_data();
        List<HormoneDTO> dailyHormoneDatas = hormone_data_new.stream()
                .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .collect(Collectors.toList());
        // 如果今天没有测试数据，就什么都不做
        if (dailyHormoneDatas.isEmpty()) {
            log.info("userId:{} today:{} dailyHormoneDatas is empty", userId, today);
            return;
        }
        // 如果今天最后一条测试数据有异常，就什么都不做
        HormoneDTO todayLastHormoneDTO = dailyHormoneDatas.get(dailyHormoneDatas.size() - 1);
        HormoneDTO.TestResult todayLastTestResult = todayLastHormoneDTO.getTest_results();
        if (todayLastTestResult.getEcode() != null && !todayLastTestResult.getEcode().startsWith("B")) {
            return;
        }
        Integer todayLastWandType = todayLastTestResult.getWand_type();

        List<HormoneDTO> historyValidPdgDataList = newHormoneDataRequest.getHormone_data_history();
        if (!historyValidPdgDataList.isEmpty()) {
            historyValidPdgDataList = historyValidPdgDataList.stream()
                    .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                    .filter(hormoneDTO ->
                            WandTypeEnum.PDG.getInteger().equals(hormoneDTO.getTest_results().getWand_type()) ||
                                    WandTypeEnum.LH_E3G_PDG.getInteger().equals(hormoneDTO.getTest_results().getWand_type())
                    )
                    .collect(Collectors.toList());
        }

        if (historyValidPdgDataList.isEmpty()) {
            if (WandTypeEnum.PDG.getInteger().equals(todayLastWandType) ||
                    WandTypeEnum.LH_E3G_PDG.getInteger().equals(todayLastWandType)) {
                // PDG首次测试，提醒用户
                PushTokenDTO pushToken = cacheManager.getPushToken(userId);
                if (StringUtils.isBlank(pushToken.getPushToken())) {
                    log.error("pdg first test, user:{} push token is empty", userId);
                    return;
                }
                messageProvider.sendNotification(new PushNotificationDTO()
                        .setUserId(userId)
                        .setTimeZone(timeZone)
                        .setPlatform(pushToken.getPlatform())
                        .setDefineId(NotificationDefineEnum.PDG_FIRST_TEST.getDefineId())
                        .setPushFirebase(Boolean.TRUE)
                        .setSaveRecord(Boolean.TRUE)
                        .setExpireTime(ZoneDateUtil.endDayTimestamp(timeZone))
                        .setTokens(Collections.singletonList(pushToken.getPushToken())));
            }
        }

        Long notificationDefineId = NewHormoneNotificationCheckUtil.getNotificationDefineId(timeZone,
                today, cycleDataDTOS, hormone_data_new, dailyHormoneDatas, todayLastTestResult, todayLastWandType);
        if (notificationDefineId != null) {
            PushTokenDTO pushToken = cacheManager.getPushToken(userId);
            if (StringUtils.isBlank(pushToken.getPushToken())) {
                log.error("define:{}, user:{} push token is empty", notificationDefineId, userId);
                return;
            }
            messageProvider.sendNotification(new PushNotificationDTO()
                    .setUserId(userId)
                    .setTimeZone(timeZone)
                    .setPlatform(pushToken.getPlatform())
                    .setDefineId(notificationDefineId)
                    .setPushFirebase(Boolean.TRUE)
                    .setSaveRecord(Boolean.TRUE)
                    .setExpireTime(ZoneDateUtil.endDayTimestamp(timeZone))
                    .setTokens(Collections.singletonList(pushToken.getPushToken())));
        }
    }

    private void editPeriod(Long userId) {
        log.info("user:{}, edit period by CONSUMER_MAX_FIRST", userId);

        AlgorithmEditPeriodDTO algorithmEditPeriodDTO = userProvider.buildAlgorithmEditPeriodDTO(userId, AlgorithmRequestTypeEnum.CONSUMER_MAX_FIRST).getData();
        algorithmService.algorithmEditPeriod(algorithmEditPeriodDTO);
    }

    /**
     * Today's Count of (LH > threshold && deadline=6:00 pm) == 1; 写入sys_notification_threshold_statistics
     * <p>
     * 设置推送时间点：Current local time = 5:40 pm;
     */
    private void checkLHTestAgainReminder(Long userId, NewHormoneDataRequest newHormoneDataRequest, AlgorithmReturnDTO algorithmReturnDTO) {
        long currentTimeMillis = System.currentTimeMillis();
        String today = newHormoneDataRequest.getToday();
        String timeZone = newHormoneDataRequest.getTimeZone();
        //[Rule1: push timing：current local time > 5:40 pm]
        long testAgainStartPushLine = ZoneDateUtil.timestamp(timeZone, today, DatePatternConst.DATE_PATTERN)
                + 17 * 3600 * 1000 + 40 * 60 * 1000;
        if (currentTimeMillis >= testAgainStartPushLine) {
            return;
        }
        List<HormoneDTO> hormone_data_new = newHormoneDataRequest.getHormone_data_new();

        //[Rule2: hormone_data_new contains LH data]
        List<HormoneDTO> dailyLhHormoneDatas = hormone_data_new.stream()
                .filter(hormoneData -> today.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .filter(hormoneData -> hormoneData.getTest_results().getEcode() == null || hormoneData.getTest_results().getEcode().startsWith("B"))
                .filter(hormoneData -> WandTypeEnum.LH.getInteger().equals(hormoneData.getTest_results().getWand_type())
                        || WandTypeEnum.E3G_LH.getInteger().equals(hormoneData.getTest_results().getWand_type())
                        || WandTypeEnum.LH_E3G_PDG.getInteger().equals(hormoneData.getTest_results().getWand_type()))
                .collect(Collectors.toList());
        if (dailyLhHormoneDatas.isEmpty()) {
            return;
        }
        // 取当前周期的LH阈值
        Float currentCycleThresholdLh = null;
        List<CycleDataDTO> cycleDataDTOS = algorithmReturnDTO.getCycle_data();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            int subtractFromPeriodStart = LocalDateUtil.minusToDay(today, datePeriodStart);
            if (subtractFromPeriodStart < 0 || subtractFromPeriodStart >= cycleDataDTO.getLen_cycle()) {
                continue;
            }
            currentCycleThresholdLh = cycleDataDTO.getThreshold_LH();
        }
        if (currentCycleThresholdLh == null) {
            return;
        }
        int countOfLhThreshold = 0;
        for (HormoneDTO hormoneDTO : dailyLhHormoneDatas) {
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            Float lhValue = null;
            if (WandTypeEnum.LH.getInteger().equals(testResult.getWand_type())) {
                lhValue = testResult.getValue1();
            } else if (WandTypeEnum.E3G_LH.getInteger().equals(testResult.getWand_type())) {
                lhValue = testResult.getValue2();
            } else if (WandTypeEnum.LH_E3G_PDG.getInteger().equals(testResult.getWand_type())) {
                lhValue = testResult.getValue1();
            }
            if (lhValue != null && lhValue >= currentCycleThresholdLh) {
                countOfLhThreshold++;
            }
        }
        //[Rule3: Count of (LH > threshold) >0]
        if (countOfLhThreshold == 0) {
            return;
        }

        // hormone_data_new contains Max wand?
        Long notificationDefineId = NotificationDefineEnum.TEST_REMINDER_LH_SURGE_AGAIN_WITH_MAX.getDefineId();

        messageProvider.updateNotificationStatistics(new StatisticsNotificationDTO()
                .setUserId(userId)
                .setTimeZone(timeZone)
                .setDefineId(notificationDefineId)
                .setTestAgainStartPushLine(testAgainStartPushLine));
    }

    private void processPatientBiz(Long userId, NewHormoneDataRequest newHormoneDataRequest, AlgorithmReturnDTO algorithmReturnDTO) {
        List<HormoneDTO> hormone_data_new = newHormoneDataRequest.getHormone_data_new();
        String today = newHormoneDataRequest.getToday();
        List<HormoneDTO> historyValidDataList = newHormoneDataRequest.getHormone_data_history();
        if (!historyValidDataList.isEmpty()) {
            historyValidDataList = historyValidDataList.stream()
                    .filter(HormoneDTO -> HormoneDTO.getFlag() == 1)
                    .collect(Collectors.toList());
        }
        if (historyValidDataList.isEmpty() && !hormone_data_new.isEmpty()) {
            List<HormoneDTO> newValidDataList = hormone_data_new.stream()
                    .filter(HormoneDTO -> HormoneDTO.getFlag() == 1)
                    .collect(Collectors.toList());
            if (!newValidDataList.isEmpty()) {
                // 通知护士:第一条有效的测试数据
                patientProducer.newHormone(userId, EmailTypeEnum.NURSE_FIRST_TEST);
            }
        }

        List<CycleDataDTO> CycleDataDTOS = algorithmReturnDTO.getCycle_data();
        for (CycleDataDTO CycleDataDTO : CycleDataDTOS) {
            String datePeriodStart = CycleDataDTO.getDate_period_start();
            String dateLhSurge = CycleDataDTO.getDate_LH_surge();
            Integer lenCycle = CycleDataDTO.getLen_cycle();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);

            // 判断今天在这个周期内
            if (LocalDateUtil.minusToDay(today, datePeriodStart) >= 0 &&
                    LocalDateUtil.minusToDay(today, dateCycleEnd) < 0) {
                if (StringUtils.isNotBlank(dateLhSurge)) {
                    // dateLhSurge命中了当前测试数据的测试时间
                    hormone_data_new.stream()
                            .filter(HormoneDTO -> dateLhSurge.equals(HormoneDTO.getTest_time()))
                            .findAny()
                            .ifPresent(lhSurgeHormoneDTO
                                    -> patientProducer.newHormone(userId, EmailTypeEnum.NURSE_LH_SURGE));
                }
            }
        }
    }

    private void processWandsBiz(Long userId, NewHormoneDataRequest newHormoneDataRequest) {
        List<HormoneDTO> hormone_data_history = newHormoneDataRequest.getHormone_data_history();
        List<HormoneDTO> hormone_data_new = newHormoneDataRequest.getHormone_data_new();
        // 如果历史数据不包含有效的MAX试剂，且当次测试数据包含有效的MAX试剂，则数据上传之后立马编辑一次经期，当前目的仅为了使未来周期的测试日变为推荐测试MAX
        boolean historyContainMaxWands = CheckWandsUtil.containMaxWands(hormone_data_history);
        boolean newContainMaxWands = CheckWandsUtil.containMaxWands(hormone_data_new);
        // 如果历史数据不包含有效的Fsh试剂，且当次测试数据包含有效的Fsh试剂，则数据上传之后立马编辑一次经期，当前目的仅为了使未来周期的测试日变为推荐测试Fsh
        boolean historyContainFshWands = CheckWandsUtil.containFshWands(hormone_data_history);
        boolean newContainFshWands = CheckWandsUtil.containFshWands(hormone_data_new);
        if ((!historyContainMaxWands && newContainMaxWands) || (!historyContainFshWands && newContainFshWands)) {
            // 编辑经期
            editPeriod(userId);
        }
    }

    private void processSnLimit(Long userId, NewHormoneDataRequest newHormoneDataRequest, NewHormoneEventDTO newHormoneEventDTO) {
        String timeZone = newHormoneDataRequest.getTimeZone();
        String today = newHormoneDataRequest.getToday();

        String currentSn = newHormoneEventDTO.getCurrentSn();
        // sn limit校验
        int limitNumber;
        SnDataLimitEntity snDataLimitEntity = snDataLimitDAO.getBySn(currentSn);
        if (snDataLimitEntity != null) {
            limitNumber = snDataLimitEntity.getNumber();
        } else {
            limitNumber = sysDictProperties.getSnLimit();
        }
        // 校验该sn在今天是否有超过limitNumber数量的有效测试数据
        long historyCount = appDataUploadDAO.countValidDataBySnAndDay(currentSn, today);
        if (historyCount >= limitNumber && !cacheManager.isInDataUploadWhiteList(userId)) {
            log.info("sn:{} limitNumber:{} 大于 historyCount:{}，将会锁定", currentSn, limitNumber, historyCount);
            // 给sn加锁，加锁时间一直到用户的当天晚上23:59:59
            Long todayLastTime = ZoneDateUtil.timestamp(timeZone, today + " 23:59:59", DatePatternConst.DATE_TIME_PATTERN);
            cacheManager.cacheRestrictDevice(currentSn, todayLastTime - System.currentTimeMillis());
            CompletableFuture.runAsync(() -> {
                        List<String> receivers = Arrays.asList("<EMAIL>", "<EMAIL>");
                        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
                        Map<String, String> emailVariable = new HashMap<>();
                        emailVariable.put("userId", userId.toString());
                        emailVariable.put("sn", currentSn);
                        emailVariable.put("limit", String.valueOf(limitNumber));
                        emailVariable.put("historyCount", String.valueOf(historyCount));
                        emailVariable.put("email", appUserDTO.getEmail());
                        emailProducer.sendSnLimitEmail(userId, timeZone, receivers, emailVariable);
                    }, ThreadPoolUtil.getPool()
            ).exceptionally(ex -> {
                log.error("send sn limit email error", ex);
                return null;
            });
            CompletableFuture.runAsync(() -> {
                        PushTokenDTO pushToken = cacheManager.getPushToken(userId);
                        if (StringUtils.isBlank(pushToken.getPushToken())) {
                            log.error("sn limit, user:{} push token is empty", userId);
                            return;
                        }
                        messageProvider.sendNotification(new PushNotificationDTO()
                                .setUserId(userId)
                                .setTimeZone(timeZone)
                                .setPlatform(pushToken.getPlatform())
                                .setDefineId(NotificationDefineEnum.TOO_MANY_TEST_DATA.getDefineId())
                                .setExtend(String.valueOf(limitNumber))
                                .setPushFirebase(Boolean.TRUE)
                                .setSaveRecord(Boolean.TRUE)
                                .setExpireTime(ZoneDateUtil.endDayTimestamp(timeZone))
                                .setTokens(Collections.singletonList(pushToken.getPushToken())));
                    }, ThreadPoolUtil.getPool()
            ).exceptionally(ex -> {
                log.error("send sn limit push error", ex);
                return null;
            });
        }
    }

    private void verifyWandsExceptionAlarm(Long userId, NewHormoneDataRequest newHormoneDataRequest) {
        //sample error： error in (03,05)
        //2. **Condition**
        //    - Condition I: Current data is sample error
        //    - Condition II: Detecting non-same test wand in historical data
        //        - **Exception Status 1**: One and only one sample error for the day
        //        - **Exception Status 2**: Multiple sample errors in the day.
        //        - **Exception Status 3**: No sample errors for the day, but the last test data is sample error(not the same date).
        //        - **Exception Status 4**: No sample errors for the day, and the last valid test data is not a sample error
        //        (not the same date), but there were a total of 3 sample errors or more in the week forward from the current time.
        List<HormoneDTO> hormoneDataNew = newHormoneDataRequest.getHormone_data_new();
        List<HormoneDTO> hormoneDataHistory = newHormoneDataRequest.getHormone_data_history();
        String timeZone = newHormoneDataRequest.getTimeZone();
        List<HormoneDTO> e0305List = new ArrayList<>();
        hormoneDataNew.forEach(
                newHormoneDTO -> {
                    String e_code = newHormoneDTO.getTest_results().getEcode();
                    if (DeviceErrorCodeEnum.E03.getValue().equals(e_code)
                            || DeviceErrorCodeEnum.E05.getValue().equals(e_code)) {
                        e0305List.add(newHormoneDTO);
                    }
                }
        );
        if (e0305List.isEmpty()) {
            // 如果新传入的数据中没有e0305的数据，说明不需要进行异常告警处理
            //todo
            return;
        }
        e0305List.sort((Comparator.comparing(HormoneDTO::getTest_time)));

        HormoneDTO currentErrorHormoneDTO;
        if (!e0305List.isEmpty()) {
            currentErrorHormoneDTO = e0305List.get(e0305List.size() - 1);
        } else {
            currentErrorHormoneDTO = hormoneDataNew.get(0);
        }

        String wandTestTime = currentErrorHormoneDTO.getTest_time();
        Integer wandType = currentErrorHormoneDTO.getTest_results().getWand_type();
        String ecode = currentErrorHormoneDTO.getTest_results().getEcode();
        //最新的一条异常数据所在的那一天
        String thisDay = LocalDateUtil.dateTime2Date(wandTestTime);
        log.info("user:{}: 新数据中 e0305List size:{}, 当前sample error数据测试时间:【{}】,试剂类型:【{}】,错误码:【{}】",
                userId, e0305List.size(), wandTestTime, wandType, ecode);
        hormoneDataHistory.forEach(
                historyHormoneDTO -> {
                    String e_code = historyHormoneDTO.getTest_results().getEcode();
                    if (DeviceErrorCodeEnum.E03.getValue().equals(e_code)
                            || DeviceErrorCodeEnum.E05.getValue().equals(e_code)) {
                        e0305List.add(historyHormoneDTO);
                    }
                }
        );

        List<HormoneDTO> thisDayE0305List =
                e0305List.stream()
                        .filter(hormoneData -> thisDay.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                        .collect(Collectors.toList());
        WandsExceptionAlarmEnum alarmCodeEnum = null;
        if (thisDayE0305List.size() == 2) {
            alarmCodeEnum = WandsExceptionAlarmEnum.One;
        } else if (thisDayE0305List.size() > 2) {
            alarmCodeEnum = WandsExceptionAlarmEnum.Two;
        } else {
            String lowerBoundDate = LocalDateUtil.plusDay(thisDay, -7, DatePatternConst.DATE_PATTERN);
            long e0305WeekCount = e0305List.stream()
                    .filter(hormoneData ->
                            LocalDateUtil.isBetweenDateAndEqual(hormoneData.getTest_time(),
                                    lowerBoundDate, thisDay)
                    )
                    .count();
            if (e0305WeekCount >= 3) {
                alarmCodeEnum = WandsExceptionAlarmEnum.Four;
            } else {
                //当天只有一条sample error数据，且没有命中其他条件，忽略告警
                return;
            }
        }

        WandsExceptionAlarmDTO wandsExceptionAlarmDTO = new WandsExceptionAlarmDTO();
        wandsExceptionAlarmDTO.setUserId(userId);
        wandsExceptionAlarmDTO.setTimeZone(timeZone);
        wandsExceptionAlarmDTO.setEcode(ecode);
        wandsExceptionAlarmDTO.setWandType(wandType);
        wandsExceptionAlarmDTO.setWandTestTime(wandTestTime);
        wandsExceptionAlarmDTO.setTriggerTime(ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));

        wandsExceptionAlarmDTO.setAlarmCode(alarmCodeEnum.getCode());
        wandsExceptionAlarmDTO.setDetail(alarmCodeEnum.getDesc());

        if (mongoRefProperties.mongoSwitchOpen()) {
            String finalAlarmCode = alarmCodeEnum.getCode();
            CompletableFuture.runAsync(() -> {
                log.info("试剂异常告警保存到mongo:user:{},alarmCode:{}", userId, finalAlarmCode);
                AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
                wandsExceptionAlarmDTO.setEmail(appUserDTO.getEmail());
                wandsExceptionAlarmProvider.saveOrUpdate(wandsExceptionAlarmDTO);
            }, ThreadPoolUtil.getPool()).exceptionally(e -> {
                log.error("user:{},alarmCode:{} save wands exception alarm to mongo error", userId, finalAlarmCode, e);
                return null;
            });
        }
    }

    private void processPayWall(Long userId, NewHormoneDataRequest newHormoneDataRequest,
                                AlgorithmReturnDTO algorithmReturnDTO) {
        long historyCount = appDataUploadDAO.countValidDataByUserId(userId);

        UserPaywallDTO userPaywallDTO = new UserPaywallDTO();
        userPaywallDTO.setUserId(userId);
        userPaywallDTO.setHistoryCount(historyCount);
        userPaywallDTO.setTimeZone(newHormoneDataRequest.getTimeZone());
        userPaywallDTO.setCycleData(algorithmReturnDTO.getCycle_data());
        userProvider.payWall(userPaywallDTO);
    }

    private void processTestingReminder(Long userId, NewHormoneDataRequest newHormoneDataRequest) {
        List<HormoneDTO> hormoneDataNew = newHormoneDataRequest.getHormone_data_new();
        Set<String> wandTypeSet = hormoneDataNew.stream()
                .map(hormone -> String.valueOf(hormone.getTest_results().getWand_type()))
                .collect(Collectors.toSet());

        String currentDay = ZoneDateUtil.format(newHormoneDataRequest.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        String key = RedisCacheKeyConst.USER_NEW_HORMONE_WANDTYPE_DAY + currentDay + ":" + userId.toString();
        Set<String> existSet = redisComponent.get(key, Set.class);
        if (CollectionUtils.isEmpty(existSet)) {
            redisComponent.setEx(key, wandTypeSet, 36, TimeUnit.HOURS);
            log.info("new normone test, wand:{}", JsonUtil.toJson(wandTypeSet));
        } else {
            existSet.addAll(wandTypeSet);
            Long ttl = redisComponent.ttl(key);
            if (ttl < 1L) {
                ttl = 3600L;
            }
            redisComponent.setEx(key, existSet, ttl, TimeUnit.SECONDS);
            log.info("new normone test, update wand:{}", JsonUtil.toJson(existSet));
        }
    }

    private void markUserTestCompleted(Long userId) {
        // pub event
        String eventId = UUID.randomUUID().toString();
        Map<String, String> event = new HashMap<>();
        event.put("user_id", userId.toString());
        event.put("event", "user_test_completed");
        event.put("event_id", eventId);

        redisComponent.publish("user_events", JsonUtil.toJson(event));
        log.info("publish user_events, user_id:{}, event_id:{}", userId, eventId);
    }
}