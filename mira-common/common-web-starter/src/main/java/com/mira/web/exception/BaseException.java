package com.mira.web.exception;

import com.mira.core.response.enums.BaseCodeEnum;
import com.mira.core.response.enums.ICodeEnum;
import lombok.Getter;

/**
 * Base Exception
 *
 * <AUTHOR>
 */
@Getter
public class BaseException extends RuntimeException {
    private final Integer code;
    private final String msg;

    public BaseException(ICodeEnum iCodeEnum) {
        super(iCodeEnum.getMsg());
        this.code = iCodeEnum.getCode();
        this.msg = iCodeEnum.getMsg();
    }

    public BaseException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BaseException(String msg) {
        super(msg);
        this.code = BaseCodeEnum.INTERNAL_SERVER_ERROR.getCode();
        this.msg = msg;
    }
}
