package com.mira.mongo.repository;

import com.mira.mongo.domain.Survey;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-03-22 18:23
 **/
@Repository
public interface SurveyRepository extends MongoRepository<Survey, String> {
    @Query(value = "{}")
    Page<Survey> findLatestSurvey(Pageable pageable);

    @Query("{ 'status': 1, 'startTime': { '$lte': ?0 }, 'endTime': { '$gte': ?0 } }")
    List<Survey> findActiveSurveysWithinTimeRange(String localtimeStr);

    @Query("{ 'status': 1 }")
    List<Survey> findActiveSurveys();
}