package com.mira.mongo.service;

import com.mira.api.mongo.dto.SurveyAnswerDTO;
import com.mira.api.mongo.dto.SurveyBaseDTO;
import com.mira.api.mongo.dto.SurveyDTO;
import com.mira.api.mongo.dto.SurveyPageVO;
import com.mira.core.request.PageDTO;

import java.util.List;

/**
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-03-24 22:34
 **/
public interface ISurveyService {
    void saveOrUpdateSurvey(SurveyDTO surveyDTO);

    void changeSurveyStatus(String id, Integer status);

    void deleteSurvey(String id);

    SurveyPageVO surveyPage(PageDTO pageDTO);

    SurveyDTO getSurveyById(String id);

    void saveSurveyAnswer(SurveyAnswerDTO surveyAnswerDTO);


    /**
     * 1. survey必须是激活状态
     * 2. 当前时间必须在开始时间和截止时间之间
     * 3. 如果用户已经对某个survey回答过，就不会返回该survey
     * 4. 如果用户在view log中对某个survey id的view times存在3次，就不返回了
     * 5. 返回满足以上情况的所有survey
     *
     * @param userId   用户Id
     * @param timeZone 时区
     * @return List<SurveyDTO>
     */
    List<SurveyDTO> listSurveyByUserId(Long userId, String timeZone);

    void viewSurvey(String id, Long userId, String timeZone);

    List<SurveyBaseDTO> listAll();

    List<SurveyBaseDTO> bannerSelectList();

    Boolean verifyBannerSurveySelect(Long userId, List<String> surveyIds);

    Boolean verifyBindSurveyIds(Long userId, Integer surveyCheckType, List<String> surveyIds);
}