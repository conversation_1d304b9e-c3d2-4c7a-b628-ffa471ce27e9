package com.mira.mongo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.mira.mongo.DTO.PcosSurveyAnswerDTO;
import com.mira.mongo.DTO.SurveyAnswerAnalysisDTO;
import com.mira.mongo.DTO.SurveyAnswerAnalysisPageVO;
import com.mira.mongo.domain.SurveyAnswer;
import com.mira.mongo.repository.SurveyAnswerRepository;
import com.mira.mongo.repository.SurveyHistoryRepository;
import com.mira.mongo.repository.SurveyRepository;
import com.mira.mongo.service.ISurveyAnalysisService;
import com.mira.mongo.util.SurveyExcelExporterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-05-06
 **/
@Slf4j
@Service
public class SurveyAnalysisServicImpl implements ISurveyAnalysisService {
    @Resource
    private SurveyRepository surveyRepository;
    @Resource
    private SurveyHistoryRepository surveyHistoryRepository;
    @Resource
    private SurveyAnswerRepository surveyAnswerRepository;

    @Override
    public SurveyAnswerAnalysisPageVO pageSurveyAnswer(String surveyId) {
        Integer current = 1;
        Integer size = 20;

        // 创建分页请求对象，按modifyTime降序排序
        Pageable pageable = PageRequest.of(current - 1, size, Sort.by(Sort.Direction.ASC, "modifyTime"));
        // 执行查询
        Page<SurveyAnswer> page = surveyAnswerRepository.findAll(pageable);
        // 将实体转换为VO对象
        List<SurveyAnswerAnalysisDTO> surveyAnswerAnalysisDTOS = page.getContent().stream()
                                                                     .map(this::convertToSurveyAnswerAnalysisPageDTO)
                                                                     .collect(Collectors.toList());
        SurveyAnswerAnalysisPageVO surveyAnswerAnalysisPageVO = new SurveyAnswerAnalysisPageVO();
        surveyAnswerAnalysisPageVO.setTotal(page.getTotalElements());
        surveyAnswerAnalysisPageVO.setSurveyAnswerAnalysisDTOS(surveyAnswerAnalysisDTOS);
        return surveyAnswerAnalysisPageVO;
    }

    private SurveyAnswerAnalysisDTO convertToSurveyAnswerAnalysisPageDTO(SurveyAnswer surveyAnswer) {
        SurveyAnswerAnalysisDTO dto = new SurveyAnswerAnalysisDTO();
        BeanUtil.copyProperties(surveyAnswer, dto, "id", "surveyId");
        dto.setId(surveyAnswer.getId().toString());
        //        dto.setSurveyId(surveyAnswer.getSurveyId().toString());
        dto.setAnswerTime(surveyAnswer.getModifyTimeStr());
        Object surveyAnswerObj = surveyAnswer.getSurveyAnswer();
        String surveyAnswerJsonStr = JSONUtil.toJsonStr(surveyAnswerObj);
        PcosSurveyAnswerDTO surveyAnswerDTO = JSONUtil.toBean(surveyAnswerJsonStr, PcosSurveyAnswerDTO.class);
        dto.setSurveyAnswer(surveyAnswerDTO);
        return dto;
    }

    @Override
    public void exportSurveyAnswerExcel(String surveyId) {
        List<SurveyAnswer> surveyAnswers = surveyAnswerRepository.findAll();
        List<SurveyAnswerAnalysisDTO> surveyAnswerAnalysisDTOS = surveyAnswers.stream()
                                                                              .map(this::convertToSurveyAnswerAnalysisPageDTO)
                                                                              .collect(Collectors.toList());
        try {
            SurveyExcelExporterUtil.exportSurveyAnswersToExcel(surveyAnswerAnalysisDTOS, "survey_answers.xlsx");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
