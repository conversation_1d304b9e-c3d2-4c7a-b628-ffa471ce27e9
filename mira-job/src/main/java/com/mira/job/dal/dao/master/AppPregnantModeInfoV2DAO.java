package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppPregnantModeInfoV2Entity;
import com.mira.job.dal.mapper.master.AppPregnantModeInfoV2Mapper;
import org.springframework.stereotype.Repository;

/**
 * app_pregnant_mode_info_v2 DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppPregnantModeInfoV2DAO extends ServiceImpl<AppPregnantModeInfoV2Mapper, AppPregnantModeInfoV2Entity> {
    @DS(DataSourceName.SLAVE)
    public long getCount() {
        return count(Wrappers.<AppPregnantModeInfoV2Entity>lambdaQuery()
                .eq(AppPregnantModeInfoV2Entity::getDeleted, 0));
    }
}
