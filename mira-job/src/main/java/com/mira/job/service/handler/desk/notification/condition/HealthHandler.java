package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Health
 *
 * <AUTHOR>
 */
@Component
public class HealthHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer[] healthCondition = dto.getHealthCondition();
        if (healthCondition == null || healthCondition.length == 0) {
            return;
        }
        List<Integer> healthConditionList = List.of(healthCondition);

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            String userConditions = userInfo.getConditions();
            if (StringUtils.isBlank(userConditions)) {
                waitDeleteList.add(userInfo);
                continue;
            }
            List<Integer> userConditionList = Stream.of(userConditions.split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            // 做交集，没有值代表没有命中
            List<Integer> intersection = userConditionList.stream()
                    .filter(healthConditionList::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(intersection)) {
                waitDeleteList.add(userInfo);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
