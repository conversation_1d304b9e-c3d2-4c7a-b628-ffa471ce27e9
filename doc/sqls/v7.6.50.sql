create table sys_rate_menopause_log
(
    id              bigint auto_increment comment '主键'
        primary key,
    user_id         bigint            not null comment '用户id',
    score           tinyint default 0 not null comment '得分',
    creator         bigint  default 0 not null comment '创建者ID 默认0',
    modifier        bigint  default 0 not null comment '修改者ID 默认0',
    sys_note        varchar(512)      null comment '系统操作备注',
    create_time     bigint            null comment '创建时间',
    modify_time     bigint            null comment '修改时间',
    create_time_str varchar(255)      null comment '创建时间',
    modify_time_str varchar(255)      null comment '修改时间',
    time_zone       varchar(100)      not null comment '时区',
    deleted         tinyint default 0 not null comment '是否删除 0否,1是 (默认0)',
    constraint user_id
        unique (user_id)
)
    comment '用户menopause好评邀请评价记录表';

create table app_user_onboarding_page_view
(
    id              bigint auto_increment comment '主键'
        primary key,
    user_id         bigint            not null comment '用户id',
    page_view       varchar(255)      null comment '页面编码',
    creator         bigint  default 0 not null,
    modifier        bigint  default 0 not null,
    deleted         tinyint default 0 not null,
    create_time     bigint            not null,
    modify_time     bigint            not null,
    create_time_str varchar(255)      not null,
    modify_time_str varchar(255)      not null,
    time_zone       varchar(100)      not null,
    sys_note        varchar(512)      null
)
    comment 'onboarding页面编码保存记录';

create unique index user_idx
    on app_user_onboarding_page_view (user_id);

