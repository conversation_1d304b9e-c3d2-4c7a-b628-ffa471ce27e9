package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.user.provider.IUserProvider;
import com.mira.api.clinic.dto.DoctorPatientDTO;
import com.mira.api.message.dto.ClinicPageNotificationRecordDTO;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.clinic.async.EmailProducer;
import com.mira.clinic.controller.dto.NotificationRecordPageDTO;
import com.mira.clinic.controller.dto.SendNotificationDTO;
import com.mira.clinic.controller.vo.ClinicNotificationRecordVO;
import com.mira.clinic.dal.dao.AppTenantDAO;
import com.mira.clinic.dal.dao.AppTenantDoctorDAO;
import com.mira.clinic.dal.dao.AppTenantPatientDAO;
import com.mira.clinic.dal.dao.TenantNotificationTemplateDAO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantEntity;
import com.mira.clinic.dal.entity.AppTenantPatientEntity;
import com.mira.clinic.dal.entity.TenantNotificationTemplateEntity;
import com.mira.clinic.dto.TenantNotificationTemplateDTO;
import com.mira.clinic.service.INotificationTemplateService;
import com.mira.clinic.service.manager.CacheManager;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 医生给病人发送通知模板接口实现
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Slf4j
@Service
public class NotificationTemplateServiceImpl implements INotificationTemplateService {
    @Resource
    private AppTenantDAO appTenantDAO;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;
    @Resource
    private TenantNotificationTemplateDAO tenantNotificationTemplateDAO;

    @Resource
    private CacheManager cacheManager;
    @Resource
    private EmailProducer emailProducer;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private IMessageProvider messageProvicer;

    @Override
    public List<TenantNotificationTemplateDTO> templateList() {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);

        return tenantNotificationTemplateDAO.listByTenantcode(appTenantDoctor.getTenantCode()).stream()
                                            .map(template -> BeanUtil.toBean(template, TenantNotificationTemplateDTO.class))
                                            .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdate(TenantNotificationTemplateDTO tenantNotificationTemplateDTO) {
        Long tenantDoctorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(tenantDoctorId);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        tenantNotificationTemplateDTO.setTenantCode(appTenantDoctor.getTenantCode());
        TenantNotificationTemplateEntity tenantNotificationTemplateEntity;
        Long id = tenantNotificationTemplateDTO.getId();
        // create
        if (id == null) {
            tenantNotificationTemplateEntity = new TenantNotificationTemplateEntity();
            BeanUtil.copyProperties(tenantNotificationTemplateDTO, tenantNotificationTemplateEntity);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantNotificationTemplateEntity);
            tenantNotificationTemplateEntity.setCreator(tenantDoctorId);
            tenantNotificationTemplateEntity.setModifier(tenantDoctorId);
            tenantNotificationTemplateDAO.save(tenantNotificationTemplateEntity);
            return;
        }
        // update
        tenantNotificationTemplateEntity = tenantNotificationTemplateDAO.getById(id);
        BeanUtil.copyProperties(tenantNotificationTemplateDTO, tenantNotificationTemplateEntity);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, tenantNotificationTemplateEntity);
        tenantNotificationTemplateEntity.setModifier(tenantDoctorId);
        tenantNotificationTemplateDAO.updateById(tenantNotificationTemplateEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        tenantNotificationTemplateDAO.removeById(id);
    }

    @Override
    public void sendNotification(SendNotificationDTO sendNotificationDTO) {
        Long tenantDoctorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(tenantDoctorId);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        String notificationTitle = sendNotificationDTO.getTitle();
        String notificationContent = sendNotificationDTO.getContent();
        Long patientId = sendNotificationDTO.getPatientId();

        AppTenantEntity appTenantEntity = appTenantDAO.getByTenantCode(tenantCode);
        String notificationIcon = appTenantEntity.getNotificationIcon();
        String clinicName = appTenantEntity.getName();
        AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(patientId);
        Long userId = appTenantPatientEntity.getUserId();

        DoctorPatientDTO doctorPatientDTO = new DoctorPatientDTO();
        doctorPatientDTO.setTenantCode(tenantCode);
        doctorPatientDTO.setUserId(userId);
        doctorPatientDTO.setPatientId(patientId);
        doctorPatientDTO.setDoctorId(tenantDoctorId);

        // firebase
        PushTokenDTO pushToken = cacheManager.getPushToken(userId);
        if (StringUtils.isBlank(pushToken.getPushToken())) {
            log.error("send notification to patient, id:{} push token is empty", userId);
        } else {
            CompletableFuture.runAsync(() -> messageProvicer.sendNotification(new PushNotificationDTO()
                    .setUserId(userId)
                    .setTimeZone(timeZone)
                    .setDefineId(NotificationDefineEnum.CLINIC_NOTIFICATION.getDefineId())
                    .setTitle(notificationTitle)
                    .setContent(notificationContent)
                    .setIcon(notificationIcon)
                    .setExtend(appTenantEntity.getName().concat("@").concat(JsonUtil.toJson(doctorPatientDTO))) // 扩展字段，诊所名称
                    .setPushFirebase(Boolean.TRUE)
                    .setSaveRecord(Boolean.TRUE)
                    .setTokens(Collections.singletonList(pushToken.getPushToken()))), ThreadPoolUtil.getPool());
        }

        // email
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        AppUserInfoDTO appUserInfoDTO = userProvider.getUserInfoById(userId).getData();
        emailProducer.sendEmailToPatient(appUserDTO, appUserInfoDTO, clinicName);
    }

    @Override
    public PageResult<ClinicNotificationRecordVO> recordPage(NotificationRecordPageDTO pageDTO) {
        Long tenantDoctorId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(tenantDoctorId);
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String tenantCode = appTenantDoctor.getTenantCode();

        Long patientId = pageDTO.getPatientId();
        AppTenantPatientEntity appTenantPatient = appTenantPatientDAO.getById(patientId);
        Long userId = appTenantPatient.getUserId();

        ClinicPageNotificationRecordDTO recordDTO = messageProvicer.getNotificationListByClinic(userId,
                tenantCode, pageDTO.getCurrent(), pageDTO.getSize()).getData();
        if (recordDTO.getTotal() == 0) {
            return new PageResult<>();
        }

        List<ClinicNotificationRecordVO> dataList = recordDTO.getList().stream()
                                                             .map(record -> BeanUtil.toBean(record, ClinicNotificationRecordVO.class))
                                                             .collect(Collectors.toList());

        return new PageResult<>(dataList, recordDTO.getTotal(), pageDTO.getSize(), pageDTO.getCurrent());
    }
}
