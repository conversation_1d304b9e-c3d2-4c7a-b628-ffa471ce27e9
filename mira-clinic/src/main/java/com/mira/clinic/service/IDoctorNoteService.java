package com.mira.clinic.service;

import com.mira.clinic.controller.dto.CreatePatientNoteDTO;
import com.mira.clinic.controller.vo.TenantDoctorNoteVO;

import java.util.List;

/**
 * 医生数据管理接口
 *
 * <AUTHOR>
 */
public interface IDoctorNoteService {
    /**
     * 增加Note
     *
     * @param createPatientNoteDTO note信息
     */
    void createPatientNote(CreatePatientNoteDTO createPatientNoteDTO);

    /**
     * 删除Note
     *
     * @param noteId noteId
     */
    void deletePatientNote(Long noteId);

    /**
     * 医生关于指定病人的note列表
     *
     * @param patientId 病人id
     * @return note列表
     */
    List<TenantDoctorNoteVO> noteList(Long patientId);
}
