package com.mira.script.dal.dao.hub_duquesne;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.script.dal.entity.hub_duquesne.HubDuquesneUserDailyMoodEntity;
import com.mira.script.dal.mapper.DataSourceName;
import com.mira.script.dal.mapper.hub_duquesne.HubDuquesneUserDailyMoodMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-11-05
 **/
@DS(DataSourceName.HUB_DUQUESNE)
@Repository
public class HubDuquesneUserDailyMoodDAO extends ServiceImpl<HubDuquesneUserDailyMoodMapper, HubDuquesneUserDailyMoodEntity> {
}
