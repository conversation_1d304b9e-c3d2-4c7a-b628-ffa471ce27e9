package com.mira.script.dal.entity.hub_duquesne;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * hub_user_cycle
 * <p>
 * from app_user_algorithm_result
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-11-05
 **/
@Getter
@Setter
@TableName("hub_user_cycle")
public class HubDuquesneUserCycleEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;
    private Integer cycleIndex;
    private Integer cycleLength;

    @ApiModelProperty("周期开始日")
    private String datePeriodStart;

    @ApiModelProperty("经期结束日（经期不包含这天")
    private String datePeriodEnd;
    @ApiModelProperty("周期状态：0:空周期；1:实周期；2:预测周期；3:怀孕周期（第一阶段）4.怀孕周期（第二阶段）5.怀孕周期（第三阶段）6.postpartum阶段 7.特殊用户周期")
    private Integer cycleStatus;
}
