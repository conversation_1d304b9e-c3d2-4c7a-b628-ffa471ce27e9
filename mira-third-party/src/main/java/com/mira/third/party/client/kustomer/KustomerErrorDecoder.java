package com.mira.third.party.client.kustomer;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.github.lianjiatech.retrofit.spring.boot.core.ErrorDecoder;
import com.mira.third.party.exception.ThirdPartyException;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * kustomer 接口错误处理
 *
 * <AUTHOR>
 */
@Component
public class KustomerErrorDecoder implements ErrorDecoder {
    @Override
    public RuntimeException invalidRespDecode(Request request, Response response) {
        if (!response.isSuccessful()) {
            assert response.body() != null;
            InputStream inputStream = response.body().byteStream();
            String json = IoUtil.read(inputStream, StandardCharsets.UTF_8);
            if (StrUtil.isEmpty(json)) {
                throw new ThirdPartyException(response.code() + json);
            }
            throw new ThirdPartyException(response.code() + json);
        } else {
            return null;
        }
    }
}
