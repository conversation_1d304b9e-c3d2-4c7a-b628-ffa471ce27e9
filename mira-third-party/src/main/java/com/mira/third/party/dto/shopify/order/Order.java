package com.mira.third.party.dto.shopify.order;

import com.mira.third.party.dto.shopify.address.Address;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-04-11
 **/
@Getter
@Setter
public class Order {
    private String id;
    private String name;
    private String created_at;
    private String updated_at;
    private String cancelled_at;
    private String closed_at;
    private String financial_status;
    private String fulfillment_status;
    private String subtotal_price;
    private PriceSet subtotal_price_set;
    private String total_price;

    private PriceSet total_price_set;

    @Getter
    @Setter
    public static class PriceSet {
        private Money shop_money;
        private Money presentment_money;

        @Getter
        @Setter
        public static class Money {
            private String amount;
            private String currency_code;
        }
    }

    private OrderAddress shipping_address;
    private OrderAddress billing_address;

    @Getter
    @Setter
    public static class OrderAddress extends Address {
        private String latitude;
        private String longitude;
    }

    private List<ShippingLine> shipping_lines;

    @Getter
    @Setter
    public static class ShippingLine {
        private String id;
        private String title;
        private String code;
        private String price;
        private PriceSet price_set;
        private String discounted_price;

        private PriceSet discounted_price_set;
    }

    private List<LineItem> line_items;

    @Getter
    @Setter
    public static class LineItem {
        private String id;
        private String name;
        private String title;
        private int quantity;
        private String price;
        private PriceSet price_set;
        private boolean product_exists;
        private String sku;
        private String total_discount;
        private PriceSet total_discount_set;
        private String admin_graphql_api_id;
    }
}
