package com.mira.api.mongo.dto.burying;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("记录进入app的时间请求参数")
public class AccessInInfoMongoDTO extends AccessInInfoDTO {
    @ApiModelProperty("user id")
    private Long userId;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("时区")
    private String timeZone;
}
