package com.mira.api.message.dto;

import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.user.dto.user.AppUserDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 用户发送邮件传输类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AppUserEmailDTO {
    private AppUserDTO appUserDTO;
    private EmailTypeEnum emailTypeEnum;
    private Map<String, String> emailVariable;
}
