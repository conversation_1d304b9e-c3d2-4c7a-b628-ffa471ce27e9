package com.mira.api.user.enums.daily;

import lombok.Getter;

/**
 * 身体状态程度
 */
@Getter
public enum DailySymptomLevelEnum {
    ZERO(null, "None"),
    ONE(1, "Mild"),
    TWO(2, "Moderate"),
    THREE(3, "Severe");

    private final Integer value;
    private final String description;

    DailySymptomLevelEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
