package com.mira.api.user.dto.user;

import com.mira.api.user.enums.TipsEmbedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("Tips")
public class SysTipsDTO {
    @ApiModelProperty("tips弹窗类型，0:default，1:new tips")
    private Integer type = 0;

    @ApiModelProperty("tip列表")
    private List<Tip> tips;

    @ApiModelProperty("免责声明")
    private Disclaimer disclaimer;

    @Getter
    @Setter
    @ApiModel("disclaimer")
    public static class Disclaimer {
        @ApiModelProperty("type:1:原生小气泡;2:H5")
        private Integer type;

        @ApiModelProperty("1.免责声明内容 2.H5链接")
        private String content;
    }

    @Getter
    @Setter
    @ApiModel("tip")
    public static class Tip {
        @ApiModelProperty("Biomarker类型，参考WandTypeEnum")
        private Integer biomarker;

        @ApiModelProperty("Title")
        private String title;

        @ApiModelProperty("tipModelData列表")
        private String template;

        @ApiModelProperty("Image")
        private String image;

        @ApiModelProperty("note")
        private String note;

        @ApiModelProperty("模版ID")
        private Integer model_id;

        @ApiModelProperty("tips中嵌入内容类型，1图片、2视频、3图表页")
        private Integer embedType = TipsEmbedTypeEnum.PICTURE.getType();
    }
}
