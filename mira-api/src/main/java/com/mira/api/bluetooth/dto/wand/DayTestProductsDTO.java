package com.mira.api.bluetooth.dto.wand;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("当天需要测试的产品")
public class DayTestProductsDTO {
    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("周期数据")
    private List<CycleDataDTO> cycleDataDTOS;

    @ApiModelProperty("试剂的测试历史记录")
    private List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS;
}
