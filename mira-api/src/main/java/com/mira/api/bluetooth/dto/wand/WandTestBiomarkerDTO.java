package com.mira.api.bluetooth.dto.wand;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("每种试剂的测试历史记录")
public class WandTestBiomarkerDTO {
    @ApiModelProperty("试剂类型，参考WandTypeEnum")
    private String wandType;

    @ApiModelProperty("hormone 测试结果数据时间戳")
    private String testTime;

    @ApiModelProperty("Warning/Error值")
    private String Ecode;

    @ApiModelProperty("测量值")
    private String testValue;

    @ApiModelProperty("是否是手动pending数据,1:是；0:否(默认)")
    private Integer pending = 0;

    @ApiModelProperty("pending数据状态，0:未处理，1:正在处理，2:已处理，3:已同步，4:未添加")
    private Integer pendingStatus;

    @ApiModelProperty("同试剂棒标记")
    private Integer sameWandMark;

    @ApiModelProperty("产品编号")
    private String productCode;

    public WandTestBiomarkerDTO(String wandType, String testTime, String ecode) {
        this.wandType = wandType;
        this.testTime = testTime;
        Ecode = ecode;
    }

    public WandTestBiomarkerDTO(String wandType, String testTime, Integer pending, Integer pendingStatus) {
        this.wandType = wandType;
        this.testTime = testTime;
        this.pending = pending;
        this.pendingStatus = pendingStatus;
    }

    public WandTestBiomarkerDTO() {
    }
}
