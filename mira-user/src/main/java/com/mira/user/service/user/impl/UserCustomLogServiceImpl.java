package com.mira.user.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.daily.DailyStatusPregnantEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.UnitConvertUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.controller.vo.diary.*;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.diary.*;
import com.mira.user.enums.calendar.CustomLogConfigEnum;
import com.mira.user.enums.user.PregnantChangeStatusEnum;
import com.mira.user.service.manager.UserCustomLogManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户日志接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("userCustomLogService")
public class UserCustomLogServiceImpl extends AbstractCustomLogService {
    @Resource
    private AppUserDiaryDAO appUserDiaryDAO;
    @Resource
    private AppUserCustomLogConfigDAO appUserCustomLogConfigDAO;
    @Resource
    private AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    @Resource
    private AppUserTemperatureDAO appUserTemperatureDAO;
    @Resource
    private AppUserDiaryMoodsDAO appUserDiaryMoodsDAO;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private UserCustomLogManager userCustomLogManager;
    @Resource
    private KlaviyoProducer klaviyoProducer;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private SysDictProperties sysDictProperties;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void configUpdate(UserCustomLogConfigDTO userCustomLogConfigDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        AppUserCustomLogConfigEntity userCustomLogConfigEntity = appUserCustomLogConfigDAO.getByUserId(loginInfo.getId());

        if (Objects.isNull(userCustomLogConfigEntity)) {
            userCustomLogConfigEntity = new AppUserCustomLogConfigEntity();
            userCustomLogConfigEntity.setUserId(loginInfo.getId());
            buildCustomLogEntity(userCustomLogConfigDTO, userCustomLogConfigEntity);
            UpdateEntityTimeUtil.setBaseEntityTime(loginInfo.getTimeZone(), userCustomLogConfigEntity);
            appUserCustomLogConfigDAO.save(userCustomLogConfigEntity);
        } else {
            buildCustomLogEntity(userCustomLogConfigDTO, userCustomLogConfigEntity);
            UpdateEntityTimeUtil.updateBaseEntityTime(loginInfo.getTimeZone(), userCustomLogConfigEntity);
            appUserCustomLogConfigDAO.updateById(userCustomLogConfigEntity);
        }
    }

    private void buildCustomLogEntity(UserCustomLogConfigDTO userCustomLogConfigDTO,
                                      AppUserCustomLogConfigEntity userCustomLogConfigEntity) {
        CustomLogConfigEnum customLogConfigEnum = CustomLogConfigEnum.get(userCustomLogConfigDTO.getKey());
        if (Objects.nonNull(customLogConfigEnum)) {
            customLogConfigEnum.execute(userCustomLogConfigEntity, userCustomLogConfigDTO);
        }
    }

    @Override
    public String diaryNote(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(userId, dateStr);

        return Objects.isNull(appUserDiaryEntity) ? "" : appUserDiaryEntity.getNotes();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void diaryNoteUpdate(UserDiaryNoteDTO userDiaryNoteDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(loginInfo.getId(), userDiaryNoteDTO.getDateStr());

        if (Objects.isNull(appUserDiaryEntity)) {
            appUserDiaryEntity = new AppUserDiaryEntity();
            appUserDiaryEntity.setUserId(loginInfo.getId());
            appUserDiaryEntity.setDiaryDay(ZoneDateUtil.timestamp(loginInfo.getTimeZone(), userDiaryNoteDTO.getDateStr(), DatePatternConst.DATE_PATTERN));
            appUserDiaryEntity.setDiaryDayStr(userDiaryNoteDTO.getDateStr());
            appUserDiaryEntity.setNotes(userDiaryNoteDTO.getNote());
            UpdateEntityTimeUtil.setBaseEntityTime(loginInfo.getTimeZone(), appUserDiaryEntity);
            appUserDiaryDAO.save(appUserDiaryEntity);

            // Klaviyo
            klaviyoProducer.firstDailyLog(loginInfo.getId(), appUserDiaryEntity.getModifyTimeStr());

        } else {
            appUserDiaryEntity.setNotes(userDiaryNoteDTO.getNote());
            UpdateEntityTimeUtil.updateBaseEntityTime(loginInfo.getTimeZone(), appUserDiaryEntity);
            appUserDiaryDAO.updateById(appUserDiaryEntity);
        }
    }

    @Override
    public List<UserSymptomVO> diarySymptom(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        return userDiaryLogManager.listUserSymptomDTO(userId, dateStr)
                .stream().map(o -> BeanUtil.toBean(o, UserSymptomVO.class))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void diarySymptomUpdate(UserDiarySymptomsDTO userDiarySymptomsDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        if (CollectionUtils.isEmpty(userDiarySymptomsDTO.getSymptoms())) {
            userDiarySymptomsDTO.setSymptoms(new ArrayList<>());
        }

        AppUserDiarySymptomsEntity userDiarySymptomsEntity = appUserDiarySymptomsDAO.getByUserIdAndDayStr(loginInfo.getId(), userDiarySymptomsDTO.getDateStr());
        if (Objects.isNull(userDiarySymptomsEntity)) {
            userDiarySymptomsEntity = new AppUserDiarySymptomsEntity();
            userDiarySymptomsEntity.setUserId(loginInfo.getId());
            userDiarySymptomsEntity.setDiaryDayStr(userDiarySymptomsDTO.getDateStr());
            userDiarySymptomsEntity.setDiaryDay(ZoneDateUtil.timestamp(loginInfo.getTimeZone(), userDiarySymptomsDTO.getDateStr(), DatePatternConst.DATE_PATTERN));
            userDiarySymptomsEntity.setSymptoms(JsonUtil.toJson(userDiarySymptomsDTO.getSymptoms()));
            UpdateEntityTimeUtil.setBaseEntityTime(loginInfo.getTimeZone(), userDiarySymptomsEntity);
            appUserDiarySymptomsDAO.save(userDiarySymptomsEntity);

            // klaviyo
            klaviyoProducer.firstDailyLog(loginInfo.getId(), userDiarySymptomsEntity.getModifyTimeStr());

        } else {
            userDiarySymptomsEntity.setSymptoms(JsonUtil.toJson(userDiarySymptomsDTO.getSymptoms()));
            UpdateEntityTimeUtil.updateBaseEntityTime(loginInfo.getTimeZone(), userDiarySymptomsEntity);
            appUserDiarySymptomsDAO.updateById(userDiarySymptomsEntity);
        }
    }

    @Override
    public UserCustomLogVO diaryInfo(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(userId, dateStr);

        UserCustomLogVO userCustomLogVO = new UserCustomLogVO();
        if (Objects.nonNull(appUserDiaryEntity)) {
            BeanUtil.copyProperties(appUserDiaryEntity, userCustomLogVO);
        }

        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        if (customLogConfigDTO.getMedications()) {
            userCustomLogVO.setMedications(userDiaryLogManager.listUserMedicine(userId, dateStr));
        }
        if (customLogConfigDTO.getTemperature()) {
            userCustomLogVO.setTemperatures(userDiaryLogManager.listTemperatureDTO(userId, dateStr));
        }

        return userCustomLogVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserCustomLogUpdateVO diaryUpdate(UserCustomLogDTO userCustomLogDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserCustomLogUpdateVO userCustomLogUpdateVO = diarySaveOrUpdate(loginInfo, userCustomLogDTO);
        editCustomLogUnitConfig(loginInfo, userCustomLogDTO.getTempUnit(), userCustomLogDTO.getWeightUnit());
        temperatureSaveOrUpdate(loginInfo, userCustomLogDTO);
        UserCustomLogConfigVO userCustomLogConfigVO = configInfo();
        if (userCustomLogConfigVO.getMedications()) {
            medicationsSaveOrUpdate(loginInfo, userCustomLogDTO);
        }
        return userCustomLogUpdateVO;
    }

    private UserCustomLogUpdateVO diarySaveOrUpdate(BaseLoginInfo loginInfo, UserCustomLogDTO userCustomLogDTO) {
        UserCustomLogUpdateVO userCustomLogUpdateVO = new UserCustomLogUpdateVO();
        AppUserDiaryEntity appUserDiaryEntity = buildUserDiaryEntity(loginInfo, userCustomLogDTO, userCustomLogUpdateVO);
        // 体重
        String weightUnit = userCustomLogDTO.getWeightUnit();
        String weightValue = userCustomLogDTO.getWeightValue();
        userDiaryLogManager.buildWeightParam(appUserDiaryEntity, weightUnit, weightValue);
        // 其他日记
        userDiaryLogManager.buildDiaryParam(userCustomLogDTO, appUserDiaryEntity);
        // save/update
        if (Objects.isNull(appUserDiaryEntity.getId())) {
            appUserDiaryDAO.save(appUserDiaryEntity);
        } else {
            appUserDiaryDAO.updateById(appUserDiaryEntity);
        }
        return userCustomLogUpdateVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserCustomLogUpdateVO pregnantUpdate(Boolean pregnant) {
        UserCustomLogUpdateVO userCustomLogUpdateVO = new UserCustomLogUpdateVO();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        // 登录用户详情
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();

        // 每日记录
        AppUserDiaryEntity appUserDiaryEntity = appUserDiaryDAO.getByUserIdAndDayStr(userId, today);
        Boolean dbPregnant = appUserDiaryEntity == null ? null : appUserDiaryEntity.getPregnant();

        //1.怀孕模式记录未怀孕；2.其他模式记录怀孕
        Integer pregnantCode = null;
        if (goalStatus.equals(UserGoalEnum.PREGNANCY_TRACKING.getValue()) && DailyStatusPregnantEnum.FALSE.getValue().equals(pregnant)
                && (dbPregnant == null || !dbPregnant.equals(pregnant))) {
            //当前用户是怀孕模式 && 前端传入参数为未怀孕 && (数据库中怀孕状态为空 || 数据库中怀孕状态为已怀孕) --> 怀孕模式记录未怀孕
            pregnantCode = PregnantChangeStatusEnum.PREGNANT_RECORD_NOT_PREGNANT.getCode();
        } else if (!goalStatus.equals(UserGoalEnum.PREGNANCY_TRACKING.getValue()) && DailyStatusPregnantEnum.TRUE.getValue().equals(pregnant)
                && (dbPregnant == null || !dbPregnant.equals(pregnant))) {
            //当前用户不是怀孕模式 && 前端传入参数为怀孕 && (数据库中怀孕状态为空 || 数据库中怀孕状态为未怀孕) --> 其他模式记录怀孕
            pregnantCode = PregnantChangeStatusEnum.OTHERS_RECORD_PREGNANT.getCode();
        } else if (!goalStatus.equals(UserGoalEnum.PREGNANCY_TRACKING.getValue()) && DailyStatusPregnantEnum.FALSE.getValue().equals(pregnant)) {
            //当前用户不是怀孕模式 && 前端传入参数为未怀孕 --> 其他模式记录未怀孕
            List<AppUserDiaryEntity> appUserDiaryEntities = appUserDiaryDAO.listByUserIdAndBetweenDay(userId, LocalDateUtil.plusDay(today, -3,
                    DatePatternConst.DATE_PATTERN), today);
            //最近三天有是否怀孕的记录
            if (appUserDiaryEntities != null && !appUserDiaryEntities.isEmpty()) {
                long count = appUserDiaryEntities.stream()
                        .filter(diaryEntity -> diaryEntity.getPregnant() != null)
                        .count();
                if (count == 0) {
                    pregnantCode = PregnantChangeStatusEnum.OTHERS_RECORD_NOT_PREGNANT_AND_FIRST.getCode();
                }
            } else {
                pregnantCode = PregnantChangeStatusEnum.OTHERS_RECORD_NOT_PREGNANT_AND_FIRST.getCode();
            }
        }

        List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
        if (hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
            pregnantCode = null;
        }

        userCustomLogUpdateVO.setPregnantCode(pregnantCode);

        if (Objects.isNull(appUserDiaryEntity)) {
            appUserDiaryEntity = new AppUserDiaryEntity();
            appUserDiaryEntity.setUserId(userId);
            appUserDiaryEntity.setDiaryDayStr(today);
            appUserDiaryEntity.setDiaryDay(ZoneDateUtil.timestamp(timeZone, today, DatePatternConst.DATE_PATTERN));
            appUserDiaryEntity.setPregnant(pregnant);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserDiaryEntity);
            appUserDiaryDAO.save(appUserDiaryEntity);

            // klaviyo
            klaviyoProducer.firstDailyLog(userId, appUserDiaryEntity.getModifyTimeStr());
        } else {
            appUserDiaryEntity.setPregnant(pregnant);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserDiaryEntity);
            appUserDiaryDAO.updateById(appUserDiaryEntity);
        }

        return userCustomLogUpdateVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editTemperatureUnit(Long userId, String tempUnit) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AppUserCustomLogConfigEntity userCustomLogConfigEntity = appUserCustomLogConfigDAO.getByUserId(userId);
        if (Objects.nonNull(userCustomLogConfigEntity)) {
            if (!tempUnit.equals(userCustomLogConfigEntity.getTempUnit())) {
                log.info("user:{} change unit preference, tempUnit{}", userId, tempUnit);
                userCustomLogConfigEntity.setTempUnit(tempUnit);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userCustomLogConfigEntity);
                appUserCustomLogConfigDAO.updateById(userCustomLogConfigEntity);
            }
        } else {
            userCustomLogConfigEntity = new AppUserCustomLogConfigEntity();
            userCustomLogConfigEntity.setUserId(userId);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userCustomLogConfigEntity);
            userCustomLogConfigEntity.setTempUnit(tempUnit);
            appUserCustomLogConfigDAO.save(userCustomLogConfigEntity);
        }

    }

    @Override
    public Map<String, BigDecimal> convertUnit(UnitCalculationDTO unitCalculationDTO) {
        BigDecimal value = UnitConvertUtil.getUnitConvert(unitCalculationDTO.getType(),
                unitCalculationDTO.getUnit(), unitCalculationDTO.getValue());
        Map<String, BigDecimal> result = new HashMap<>();
        result.put(unitCalculationDTO.getUnit(), value);
        return result;
    }

    @Override
    public UserTemperatureVO temperature(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        TemperatureDTO temperatureDTO = userDiaryLogManager.getTemperatureDTO(id);
        if (Objects.isNull(temperatureDTO)) {
            return null;
        }

        UserTemperatureVO userTemperatureVO = new UserTemperatureVO();
        BeanUtil.copyProperties(temperatureDTO, userTemperatureVO);
        userTemperatureVO.setTempUnit(customLogConfigDTO.getTempUnit());
        userTemperatureVO.setTestTime(temperatureDTO.getTempTime());
        if (TempUnitEnum.C.getValue().equals(customLogConfigDTO.getTempUnit())) {
            userTemperatureVO.setValue(temperatureDTO.getTempC().floatValue());
        } else {
            userTemperatureVO.setValue(temperatureDTO.getTempF().floatValue());
        }

        return userTemperatureVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void temperatureUpdate(UpdateTemperatureDTO updateTemperatureDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        String dateStr = LocalDateUtil.format(updateTemperatureDTO.getTempTime(),
                DatePatternConst.DATE_TIME_PATTERN, DatePatternConst.DATE_PATTERN);

        // save or update
        Integer type = updateTemperatureDTO.getType();
        if (type != 0) {
            return;
        }
        Long id = updateTemperatureDTO.getId();
        if (Objects.isNull(id)) {
            appUserTemperatureDAO.save(buildNewTemperature(loginInfo, updateTemperatureDTO, dateStr, updateTemperatureDTO.getTempUnit()));
            return;
        }
        AppUserTemperatureEntity updateTemperatureEntity = appUserTemperatureDAO.getById(id);
        appUserTemperatureDAO.updateById(buildUpdateTemperature(loginInfo, updateTemperatureDTO,
                dateStr, updateTemperatureDTO.getTempUnit(), updateTemperatureEntity));

        editCustomLogUnitConfig(loginInfo, updateTemperatureDTO.getTempUnit(), null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void temperatureDelete(DeleteTemperatureDTO deleteTemperatureDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        appUserTemperatureDAO.deleteByUserIdAndTesttime(userId, deleteTemperatureDTO.getTestTime(), 0);
    }

    @Override
    public UserDiaryMoodsVO moodInfo(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        return BeanUtil.toBean(userDiaryLogManager.getUserDiaryMoodsDTO(userId, dateStr), UserDiaryMoodsVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void moodUpdate(UserDiaryMoodsDTO userDiaryMoodsDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        String dateStr = userDiaryMoodsDTO.getDiaryDayStr();
        AppUserDiaryMoodsEntity userDiaryMoodsEntity = appUserDiaryMoodsDAO.getByUserIdAndDiaryDayStr(userId, dateStr);
        if (ObjectUtils.isEmpty(userDiaryMoodsEntity)) {
            // save
            userDiaryMoodsEntity = new AppUserDiaryMoodsEntity();
            userDiaryMoodsEntity.setUserId(userId);
            userDiaryMoodsEntity.setDiaryDayStr(dateStr);
            userDiaryMoodsEntity.setDiaryDay(ZoneDateUtil.timestamp(timeZone, dateStr, DatePatternConst.DATE_PATTERN));
            BeanUtil.copyProperties(userDiaryMoodsDTO, userDiaryMoodsEntity);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, userDiaryMoodsEntity);
            appUserDiaryMoodsDAO.save(userDiaryMoodsEntity);
        } else {
            // update
            BeanUtil.copyProperties(userDiaryMoodsDTO, userDiaryMoodsEntity);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userDiaryMoodsEntity);
            appUserDiaryMoodsDAO.updateById(userDiaryMoodsEntity);
        }
    }
}
