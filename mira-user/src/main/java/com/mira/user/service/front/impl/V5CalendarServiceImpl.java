package com.mira.user.service.front.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.bluetooth.util.HormoneDataUtil;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.enums.PhaseEnum;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.StringListUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.controller.vo.calendar.*;
import com.mira.user.controller.vo.calendar.sub.CycleDateRange;
import com.mira.user.controller.vo.calendar.sub.OvuData;
import com.mira.user.controller.vo.calendar.sub.TestsStatus;
import com.mira.user.dal.dao.AppOvulationManualDAO;
import com.mira.user.dal.dao.AppOvulationManualNoteDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppOvulationManualEntity;
import com.mira.user.dal.entity.AppOvulationManualNoteEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.dto.calendar.OvulationCustomDTO;
import com.mira.user.enums.calendar.CalendarCycleStageEnum;
import com.mira.user.enums.calendar.TestsStatusEnum;
import com.mira.user.enums.chart.DayTestSuggestEnum;
import com.mira.user.enums.chart.ProductNameEnum;
import com.mira.user.exception.UserException;
import com.mira.user.handler.noperiod.NoPeriodHandler;
import com.mira.user.properties.TestFlowSuggestProperties;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日历页接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service("v5CalendarService")
public class V5CalendarServiceImpl extends AbstractCalendarService {
    @Resource
    private CacheManager cacheManager;
    @Resource
    private AlgorithmCallManager algorithmCallManager;
    @Resource
    private TestFlowSuggestProperties testFlowSuggestProperties;
    @Resource
    private IBluetoothProvider bluetoothProvider;
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private AppOvulationManualDAO appOvulationManualDAO;
    @Resource
    private AppOvulationManualNoteDAO appOvulationManualNoteDAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public DayTestSuggestVO dayTestSuggest(String date) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // check date
        if (StringUtils.isBlank(date)) {
            date = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        }
        // today
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        // algorithm data
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        // today suggest
        if (LocalDateUtil.minusToDay(date, today) == 0) {
            return todayTestSuggest(userId, date, hormoneDTOS, cycleDataDTOS);
        }

        return new DayTestSuggestVO();
    }

    private DayTestSuggestVO todayTestSuggest(Long userId, String date,
                                              List<HormoneDTO> hormoneDTOS, List<CycleDataDTO> cycleDataDTOS) {
        // result
        DayTestSuggestVO resultVO = new DayTestSuggestVO();
        // today hormone test
        List<HormoneDTO> todayHormoneDatas = hormoneDTOS.stream()
                .filter(hormoneData -> date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                .collect(Collectors.toList());
        List<TestRemindDTO> testRemindDTOS = getTestRemind(userId, date, todayHormoneDatas, cycleDataDTOS);
        // 单试剂棒 or 多试剂棒（试纸产品，比如Max、Plus）
        Set<String> todayNeedTestProductSet = testRemindDTOS.stream()
                .map(TestRemindDTO::getProductCode)
                .collect(Collectors.toSet()); // 今天需要测试的试纸
        log.info("today need test product:{}", String.join(", ", todayNeedTestProductSet));
        int suggetWandTypeCount = todayNeedTestProductSet.size();
        // 单试剂棒
        if (suggetWandTypeCount == 1) {
            singleWandTypeSuggest(resultVO, todayNeedTestProductSet);
            return resultVO;
        }
        // 多试剂棒，今天还没有测试过
        if (suggetWandTypeCount > 1 && todayHormoneDatas.isEmpty()) {
            multipleWandTypeSuggest(resultVO, todayNeedTestProductSet, DayTestSuggestEnum.RECOMMENDED_MULTIPLE_NO_TESTS,
                    testFlowSuggestProperties.getRecommended_multiple_no_tests_image());
            return resultVO;
        }
        // 多试剂棒，今天已经测试过部分
        Set<Integer> todayAlreadyTestProductSet = todayHormoneDatas.stream()
                .map(hormone -> hormone.getTest_results().getWand_type())
                .collect(Collectors.toSet()); // 已经测试过的试纸
        if (suggetWandTypeCount > 1
                && suggetWandTypeCount != todayAlreadyTestProductSet.size()) {
            multipleWandTypeSuggest(resultVO, todayNeedTestProductSet, DayTestSuggestEnum.RECOMMENDED_MULTIPLE_PART_TESTS,
                    testFlowSuggestProperties.getRecommended_multiple_part_tests_image());
            return resultVO;
        }
        // 已经完成了今天推荐的所有测试
        if (suggetWandTypeCount > 1) {
            completeAllSuggestTest(resultVO);
            return resultVO;
        }
        // 今天没有推荐的测试
        noWandTypeSuggest(resultVO, date, cycleDataDTOS);

        return resultVO;

    }

    private List<TestRemindDTO> getTestRemind(Long userId, String date,
                                              List<HormoneDTO> fileterHormoneDatas, List<CycleDataDTO> cycleDataDTOS) {
        WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
        wandDayTestDataDTO.setDate(date);
        wandDayTestDataDTO.setHormoneDTO(fileterHormoneDatas);
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fileterHormoneDatas)) {
            wandTestBiomarkerDTOS = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTO, userId).getData();
        }
        // get test remind
        DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
        dayTestProductsDTO.setDate(date);
        dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
        dayTestProductsDTO.setWandTestBiomarkerDTOS(wandTestBiomarkerDTOS);
        return TestRemindUtil.getTestRemind(dayTestProductsDTO);
    }

    private void singleWandTypeSuggest(DayTestSuggestVO resultVO, Set<String> todayNeedTestProductSet) {
        resultVO.setSuggestType(DayTestSuggestEnum.RECOMMENDED_1_TEST.getType());
        String productCode = todayNeedTestProductSet.iterator().next();
        // 试剂产品名
        ProductNameEnum productNameEnum = ProductNameEnum.get(productCode);
        // 不同试剂棒对应不同图片
        String image;
        if (productCode.length() == 2 && productCode.indexOf("0") == 0) {
            productCode = productCode.substring(1);
        }
        switch (WandTypeEnum.get(productCode)) {
            case LH_E3G_PDG:
                image = testFlowSuggestProperties.getRecommended_1_test_image_max();
                break;
            case E3G_LH:
                image = testFlowSuggestProperties.getRecommended_1_test_image_plus();
                break;
            case PDG:
                image = testFlowSuggestProperties.getRecommended_1_test_image_pdg();
                break;
            case FSH:
                image = testFlowSuggestProperties.getRecommended_1_test_image_fsh();
                break;
            default:
                image = testFlowSuggestProperties.getRecommended_1_test_image_max();
        }
        resultVO.setImage(image);
        resultVO.setTitle("Test with ".concat(productNameEnum == null ? "unknown" : productNameEnum.getWandName()).concat(" today"));
        resultVO.setContent(getDayTestSuggestVOContent(productCode));
    }

    private void multipleWandTypeSuggest(DayTestSuggestVO resultVO, Set<String> todayNeedTestProductSet,
                                         DayTestSuggestEnum dayTestSuggestEnum, String image) {
        resultVO.setSuggestType(dayTestSuggestEnum.getType());
        resultVO.setImage(image);
        // title
        StringBuilder titleBuilder = new StringBuilder("Test with");
        int index = 0;
        int size = todayNeedTestProductSet.size();
        for (String productCode : todayNeedTestProductSet) {
            ProductNameEnum productNameEnum = ProductNameEnum.get(productCode);
            titleBuilder.append(" ").append(productNameEnum == null ? "unknown" : productNameEnum.getWandName());
            if (index != size - 1) {
                titleBuilder.append(" and");
            }
            index++;
        }
        titleBuilder.append(" today");
        resultVO.setTitle(titleBuilder.toString());
        // single wand type or multiple wand type
        resultVO.setContent(getDayTestSuggestVOContent(todayNeedTestProductSet));
    }

    private void completeAllSuggestTest(DayTestSuggestVO resultVO) {
        resultVO.setSuggestType(DayTestSuggestEnum.ALL_RECOMMENDED_TESTS_TAKEN.getType());
        resultVO.setImage(testFlowSuggestProperties.getAll_recommended_tests_taken_image());
        resultVO.setTitle("All recommended tests taken today");
        resultVO.setContent("You already completed all recommended tests for today!");
        resultVO.setButtonType(1);
        resultVO.setButtonText("Check your testing history");
    }

    private void noWandTypeSuggest(DayTestSuggestVO resultVO, String date,
                                   List<CycleDataDTO> cycleDataDTOS) {
        // current day -> algorithm end day
        List<DayTestProductsDTO> dayTestProductsDTOS = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            for (int i = 1; i <= lenCycle; i++) {
                String day = LocalDateUtil.plusDay(datePeriodStart, i - 1, DatePatternConst.DATE_PATTERN);
                if (LocalDateUtil.minusToDay(day, date) > 0) {
                    DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
                    dayTestProductsDTO.setDate(day);
                    dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
                    dayTestProductsDTO.setWandTestBiomarkerDTOS(new ArrayList<>());
                    dayTestProductsDTOS.add(dayTestProductsDTO);
                }
            }
        }
        // get future test remind
        List<TestDiaryRemindDTO> futureTestRemindList = TestRemindUtil.getTestRemind(dayTestProductsDTOS);
        StringBuilder contentBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(futureTestRemindList)) {
            // have a test remind for tomorrow
            List<TestRemindDTO> tormorrowRemindDTO = futureTestRemindList.get(0).getTestRemindDTOS();
            if (CollectionUtils.isNotEmpty(tormorrowRemindDTO)) {
                contentBuilder = futureSuggest(tormorrowRemindDTO, true, "");
            } else {
                // have a test remind for future
                int size = futureTestRemindList.size();
                for (int i = 1; i < size - 1; i++) {
                    TestDiaryRemindDTO testDiaryRemindDTO = futureTestRemindList.get(i);
                    List<TestRemindDTO> testRemindDTOS = testDiaryRemindDTO.getTestRemindDTOS();
                    if (CollectionUtils.isNotEmpty(testRemindDTOS)) {
                        contentBuilder = futureSuggest(testDiaryRemindDTO.getTestRemindDTOS(), false, testDiaryRemindDTO.getDate());
                        break;
                    }
                }
            }
        }

        resultVO.setSuggestType(DayTestSuggestEnum.NO_RECOMMENDED_TESTS.getType());
        resultVO.setImage(testFlowSuggestProperties.getNo_recommended_tests_image());
        resultVO.setTitle("No recommended tests today!");
        resultVO.setContent(contentBuilder.toString());
    }

    private StringBuilder futureSuggest(List<TestRemindDTO> testRemindDTOS, boolean tomorrow, String futureDate) {
        StringBuilder contentBuilder = new StringBuilder("Your next test is with");
        Set<String> tomorrowNeedTestProductSet = testRemindDTOS.stream()
                .map(TestRemindDTO::getProductCode)
                .collect(Collectors.toSet());
        int index = 0;
        int size = tomorrowNeedTestProductSet.size();
        for (String productCode : tomorrowNeedTestProductSet) {
            ProductNameEnum productNameEnum = ProductNameEnum.get(productCode);
            contentBuilder.append(" ").append(productNameEnum == null ? "unknown" : productNameEnum.getWandName());
            if (index != size - 1) {
                contentBuilder.append(" and");
            }
            index++;
        }

        if (tomorrow) {
            contentBuilder.append(" tomorrow");
        } else {
            contentBuilder.append(" on ").append(convertEngDateFormat(futureDate));
        }

        return contentBuilder;
    }

    private String convertEngDateFormat(String date) {
        LocalDate localDate = LocalDateUtil.getLocalDate(date);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM", Locale.ENGLISH);
        String formattedMonth = localDate.format(formatter);
        int year = localDate.getYear();

        int day = localDate.getDayOfMonth();
        String suffix;
        switch (day % 10) {
            case 1: {
                suffix = "st";
                break;
            }
            case 2: {
                suffix = "nd";
                break;
            }
            case 3: {
                suffix = "rd";
                break;
            }
            default:
                suffix = "th";
        }

        return String.format("%s %d%s, %d", formattedMonth, day, suffix, year);
    }

    private String getDayTestSuggestVOContent(String productCode) {
        String content = "";
        switch (WandTypeEnum.get(productCode)) {
            case LH_E3G_PDG:
                // Max Wands
                content = "Test LH, E3G and PdG with Mira MAX Wand";
                break;
            case E3G_LH:
                // Fertility Plus Wands
                content = "Test LH and E3G with Mira Fertility Plus Wand";
                break;
            case PDG:
                // Confirm Wands
                content = "Test PdG with Mira Confirm Wand";
                break;
            case FSH:
                // Ovum Wands
                content = "Test FSH with Mira Ovum Wand";
                break;
        }
        return content;
    }

    private String getDayTestSuggestVOContent(Set<String> productCodeSet) {
        StringBuilder contentBuilder = new StringBuilder();
        int index = 0;
        int size = productCodeSet.size();
        for (String productCode : productCodeSet) {
            if (productCode.length() == 2 && productCode.indexOf("0") == 0) {
                productCode = productCode.substring(1);
            }
            contentBuilder.append(getDayTestSuggestVOContent(productCode));
            if (index != size - 1) {
                contentBuilder.append("\n");
            }
            index++;
        }
        return contentBuilder.toString();
    }

    @Override
    public V5CalendarDataVO calendarDataV5(String startDate, String endDate) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // 获取sex日期
        List<String> sexDateList = buildSexList(userId);

        // 周期数据、激素数据
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        // 测试时间
        Set<String> testDates = HormoneDataUtil.buildTestDates(hormoneDatas);

        // user mood map (key:date)
        Map<String, UserDiaryMoodsDTO> userDiaryMoodsDTOMap = userDiaryLogManager.getUserDiaryMoodsDTOMap(userId);
        // user custom ovulation record
        AppOvulationManualEntity ovulationManualEntity = appOvulationManualDAO.getByUserId(userId);
        List<String> customOvuDataList = new ArrayList<>();
        if (Objects.nonNull(ovulationManualEntity)) {
            customOvuDataList = JsonUtil.toArray(ovulationManualEntity.getOvuData(), String.class);
        }

        // result vo
        V5CalendarDataVO calendarDataVO = new V5CalendarDataVO();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        calendarDataVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());

        List<V5DayDataVO> dayDataVOS = new ArrayList<>();
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            List<String> cycleCdIndex = cycleDataDTO.getCycle_cd_index();
            Integer lenCycle = cycleDataDTO.getLen_cycle();
            String datePeriodStart = cycleDataDTO.getDate_period_start();

            if (!buildDayDataVOCheck(startDate, endDate, cycleDataDTO)) {
                continue;
            }

            for (int i = 1; i <= lenCycle; i++) {
                String day = LocalDateUtil.plusDay(datePeriodStart, i - 1, DatePatternConst.DATE_PATTERN);
                if (LocalDateUtil.minusToDay(day, startDate) >= 0 && LocalDateUtil.minusToDay(day, endDate) < 0) {
                    DayDataVO dayDataVO = buildDayDataVO(i, day, cycleCdIndex, sexDateList, testDates);
                    // day property
                    String dayProperty = buildDayProperty(calendarDataVO.getUserMode(), cycleDataDTO, day, i);
                    // according to ovulation type
                    dayProperty = accordingOvuTypeHandleDayProperty(dayProperty, cycleDataDTO);
                    dayDataVO.setDayProperty(dayProperty);
                    // testing
                    buildDayDataVO(day, today, cycleDataDTOS, dayDataVO);
                    // ovulation type
                    if (CalendarCycleStageEnum.OVU.getValue().equals(dayProperty)
                            || CalendarCycleStageEnum.P_OVU.getValue().equals(dayProperty)) {
                        dayDataVO.setOvulationType(cycleDataDTO.getOvulation_type());
                    }
                    // fertility score
                    List<Float> fertilityScoreList = cycleDataDTO.getFertility_score_list();
                    if (CollectionUtils.isNotEmpty(fertilityScoreList)) {
                        dayDataVO.setFertilityScore(fertilityScoreList.get(i - 1));
                    }

                    V5DayDataVO v5DayDataVO = new V5DayDataVO();
                    BeanUtil.copyProperties(dayDataVO, v5DayDataVO);
                    // mood
                    UserDiaryMoodsDTO userDiaryMoodsDTO = userDiaryMoodsDTOMap.get(day);
                    if (Objects.nonNull(userDiaryMoodsDTO)) {
                        v5DayDataVO.setMood(userDiaryMoodsDTO.getMood());
                    }
                    // user custom ovulation
                    if (customOvuDataList.contains(day)) {
                        v5DayDataVO.setCustomOvulation(1);
                    }
                    dayDataVOS.add(v5DayDataVO);
                    // check no period
                    if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null
                            && CycleStatusEnum.NO_PERIOD_CYCLE.getStatus() == cycleDataDTO.getCycle_status()) {
                        NoPeriodHandler.calendarData(v5DayDataVO);
                    }
                }
            }
            calendarDataVO.setDayDataVOs(dayDataVOS);
        }

        return calendarDataVO;
    }

    private String accordingOvuTypeHandleDayProperty(String dayProperty, CycleDataDTO cycleDataDTO) {
        Integer ovulationType = cycleDataDTO.getOvulation_type();
        if (Objects.equals(OvulationTypeEnum.PREDICTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.PREDICTED_CONFIRMED.getCode(), ovulationType)) {
            if (CalendarCycleStageEnum.OVU.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.P_OVU.getValue();
            }
            if (CalendarCycleStageEnum.FW.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.P_FW.getValue();
            }
            if (CalendarCycleStageEnum.FW_START.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.P_FW_START.getValue();
            }
            if (CalendarCycleStageEnum.FW_END.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.P_FW_END.getValue();
            }
            if (CalendarCycleStageEnum.FW_ONE.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.P_FW_ONE.getValue();
            }
        }
        if (Objects.equals(OvulationTypeEnum.DETECTED.getCode(), ovulationType)
                || Objects.equals(OvulationTypeEnum.DETECTED_CONFIRMED.getCode(), ovulationType)) {
            if (CalendarCycleStageEnum.P_OVU.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.OVU.getValue();
            }
            if (CalendarCycleStageEnum.P_FW.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.FW.getValue();
            }
            if (CalendarCycleStageEnum.P_FW_START.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.FW_START.getValue();
            }
            if (CalendarCycleStageEnum.P_FW_END.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.FW_END.getValue();
            }
            if (CalendarCycleStageEnum.P_FW_ONE.getValue().equals(dayProperty)) {
                return CalendarCycleStageEnum.FW_ONE.getValue();
            }
        }
        return dayProperty;
    }

    @Override
    public V5CalendarDayLogVO calendarDayLogV5(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // vo
        V5CalendarDayLogVO calendarDayLogVO = new V5CalendarDayLogVO();
        // algorithm
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        // today
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        if (LocalDateUtil.minusToDay(dateStr, today) <= 0) {
            buildPastTimDayLog(userId, today, dateStr, calendarDayLogVO, cycleDataDTOS, hormoneDatas);
        } else {
            buildFutureTimeDayLog(dateStr, calendarDayLogVO, cycleDataDTOS);
        }
        // phase
        calendarDayLogVO.setPhase(getCyclePhase(dateStr, cycleDataDTOS));
        // tests status
        calendarDayLogVO.setTestsStatus(buildTestsStatus(userId, dateStr, today, cycleDataDTOS, hormoneDatas));
        // whether period day
        CycleDataDTO selectDateCycle = CycleDataUtil.getCurrentCycleData(dateStr, cycleDataDTOS);
        if (CycleStatusEnum.REAL_CYCLE.getStatus() == selectDateCycle.getCycle_status()
                && CycleDataUtil.inPeriod(dateStr, selectDateCycle)) {
            calendarDayLogVO.setPeriodDay(Boolean.TRUE);
        }
        // cd
        int cdByCycle = CycleDataUtil.getCDByCycle(dateStr, selectDateCycle);
        if (cdByCycle != -1) {
            calendarDayLogVO.setCd(selectDateCycle.getCycle_cd_index().get(cdByCycle));
        }

        // check no period
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            NoPeriodHandler.calendarDayLog(selectDateCycle, calendarDayLogVO);
        }

        return calendarDayLogVO;
    }

    private String getCyclePhase(String dateStr, List<CycleDataDTO> cycleDataDTOS) {
        // phase
        CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(dateStr, cycleDataDTOS);
        if (currentCycleData == null) {
            return "unknown";
        }
        PhaseEnum phaseEnum = CycleDataUtil.getPhase(dateStr, currentCycleData);
        return phaseEnum.getName();
    }

    private List<TestsStatus> buildTestsStatus(Long userId, String date, String today,
                                               List<CycleDataDTO> cycleDataDTOS, List<HormoneDTO> hormoneDTOS) {
        // 需要测试的所有试剂
        List<TestRemindDTO> todayAllNeedTestList = getTestRemind(userId, date, new ArrayList<>(), cycleDataDTOS);
        Set<String> todayAllNeedTestProductCode = todayAllNeedTestList.stream()
                .map(TestRemindDTO::getProductCode)
                .collect(Collectors.toSet());
        // 已经测试的试剂
        List<HormoneDTO> todayHormoneDatas = hormoneDTOS.stream()
                .filter(hormoneData -> {
                            Integer wandType = hormoneData.getTest_results().getWand_type();
                            return Objects.nonNull(wandType) && date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time()));
                        }
                )
                .collect(Collectors.toList());
        Set<String> todayAlreadTestProductCode = todayHormoneDatas.stream()
                .map(this::convertProductCode)
                .collect(Collectors.toSet());

        List<TestsStatus> testsStatusList = new ArrayList<>();
        // 已经测试的
        for (String productcode : todayAlreadTestProductCode) {
            TestsStatus testsStatus = new TestsStatus();
            testsStatus.setProductCode(productcode);
            testsStatus.setStatus(TestsStatusEnum.TEST_IS_COMPLETED.getCode());
            testsStatusList.add(testsStatus);
        }
        // 有推荐，但存在没测试的试剂
        todayAllNeedTestProductCode.removeIf(todayAlreadTestProductCode::contains);
        for (String productCode : todayAllNeedTestProductCode) {
            TestsStatus testsStatus = new TestsStatus();
            testsStatus.setProductCode(productCode);
            testsStatus.setStatus(LocalDateUtil.minusToDay(date, today) >= 0
                    ? TestsStatusEnum.TEST_IS_REQUIRED.getCode() : TestsStatusEnum.MISSED_TEST.getCode());
            testsStatusList.add(testsStatus);
        }
        // 没有推荐测试的
        if (CollectionUtils.isEmpty(testsStatusList)) {
            // 仅限当前周期
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            if (LocalDateUtil.minusToDay(date, currentCycleData.getDate_period_start()) >= 0) {
                TestsStatus testsStatus = new TestsStatus();
                testsStatus.setProductCode("-1");
                testsStatus.setStatus(TestsStatusEnum.TEST_IS_NOT_REQUIRED.getCode());
                testsStatusList.add(testsStatus);
            }
        }

        return testsStatusList;
    }

    private String convertProductCode(HormoneDTO hormoneDTO) {
        Integer wandType = hormoneDTO.getTest_results().getWand_type();
        String code = WandTypeEnum.get(wandType).getString();
        if (code.length() == 1) {
            return "0".concat(code);
        }
        return code;
    }

    @Override
    public void ovulationEdit(OvulationCustomDTO ovulationCustomDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // note
        AppOvulationManualNoteEntity ovulationManualNoteEntity = new AppOvulationManualNoteEntity();
        // manual record
        AppOvulationManualEntity ovulationManualEntity = appOvulationManualDAO.getByUserId(userId);
        // sort
        ovulationCustomDTO.getOvuData().sort(String::compareTo);

        if (Objects.isNull(ovulationManualEntity)) {
            // add new
            String ovuData = JsonUtil.toJson(ovulationCustomDTO.getOvuData());
            ovulationManualEntity = new AppOvulationManualEntity();
            ovulationManualEntity.setUserId(userId);
            ovulationManualEntity.setOvuData(ovuData);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, ovulationManualEntity);
            appOvulationManualDAO.save(ovulationManualEntity);
            ovulationManualNoteEntity.setAddOvu(ovuData);
        } else {
            // update
            String existOvuData = ovulationManualEntity.getOvuData();
            String updateOvuData = JsonUtil.toJson(ovulationCustomDTO.getOvuData());
            ovulationManualEntity.setOvuData(updateOvuData);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, ovulationManualEntity);
            appOvulationManualDAO.updateById(ovulationManualEntity);
            // determine if it is added or removed, or unchanged
            List<String> existOvuDataList = JsonUtil.toArray(existOvuData, String.class);
            List<String> updateOvuDataList = JsonUtil.toArray(updateOvuData, String.class);
            compareOvuDataList(existOvuDataList, updateOvuDataList, ovulationManualNoteEntity);
        }

        // save note
        ovulationManualNoteEntity.setUserId(userId);
        ovulationManualNoteEntity.setNote(ovulationCustomDTO.getNote());
        ovulationManualNoteEntity.setCreateTime(ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
        ovulationManualNoteEntity.setTimeZone(timeZone);
        appOvulationManualNoteDAO.save(ovulationManualNoteEntity);

        // login user info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        // no period mode not call algorithm
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            return;
        }

        // call algorithm
        algorithmCallManager.editDBPeriod(loginUserInfoDTO, AlgorithmRequestTypeEnum.EDIT_MANUAL_OVULATION);
    }

    private void compareOvuDataList(List<String> existOvuDataList, List<String> updateOvuDataList,
                                    AppOvulationManualNoteEntity ovulationManualNoteEntity) {
        Set<String> existSet = new HashSet<>(existOvuDataList);
        Set<String> updateSet = new HashSet<>(updateOvuDataList);

        if (existSet.equals(updateSet)) {
            // same elements
            return;
        }

        // find elements to remove (in exist but not in update)
        Set<String> toRemove = new HashSet<>(existSet);
        toRemove.removeAll(updateSet);
        if (!toRemove.isEmpty()) {
            ovulationManualNoteEntity.setRemoveOvu(JsonUtil.toJson(toRemove));
        }

        // find elements to add (in update but not in exist)
        Set<String> toAdd = new HashSet<>(updateSet);
        toAdd.removeAll(existSet);
        if (!toAdd.isEmpty()) {
            ovulationManualNoteEntity.setAddOvu(JsonUtil.toJson(toAdd));
        }
    }

    @Override
    public CycleInfoVO cycleInfo() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // periods
        AppUserPeriodEntity userPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(userPeriodEntity)) {
            throw new UserException("user period not exist");
        }
        // vo
        CycleInfoVO cycleInfoVO = new CycleInfoVO();
        List<String> periodsStr = new ArrayList<>();
        if (StringUtils.isNotBlank(userPeriodEntity.getPeriods())) {
            List<Long> dbPeriodList = StringListUtil.strToLongList(userPeriodEntity.getPeriods(), ",");
            periodsStr = PeriodUtil.periodsLong2String(dbPeriodList, userPeriodEntity.getTimeZone());
        }
        cycleInfoVO.setPeriods(periodsStr);
        cycleInfoVO.setAvgLenPeriod(userPeriodEntity.getAvgLenPeriod());
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        cycleInfoVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());

        try {
            // algorithm data
            AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
            // algorithm ovu、fw
            for (CycleDataDTO cycleData : cycleDataDTOS) {
                CycleStatusEnum cycleStatusEnum = CycleStatusEnum.get(cycleData.getCycle_status());
                if (Objects.isNull(cycleStatusEnum)) {
                    continue;
                }
                // cycle date range
                buildCycleDateRange(cycleData, cycleInfoVO);
                // ovu
                buildOvuDate(cycleData, cycleInfoVO);
                // fw
                buildFw(cycleData, cycleInfoVO);
            }
            // user custom ovu
            buildCustomOvuDate(userId, cycleInfoVO);
            // based on user's manual ovu
            List<String> customOvuDateList = cycleInfoVO.getOvuCustom().stream().map(OvuData::getDate).collect(Collectors.toList());
            cycleInfoVO.getOvu().removeIf(ovu -> customOvuDateList.contains(ovu.getDate()));
            // current cycle index
            String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            if (Objects.nonNull(currentCycleData)) {
                cycleInfoVO.setCurrentCycleIndex(currentCycleData.getCycle_index());
            }
            // predicted period
            cycleInfoVO.setPredictedPeriods(CycleDataUtil.getPeriodList(cycleDataDTOS, CycleStatusEnum.FORECAST_CYCLE));
            // no period
            if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
                cycleInfoVO.setNoPeriod(1);
            }
        } catch (UserException ex) {
            log.warn("cycle info:{}", ex.getMsg());
        }

        return cycleInfoVO;
    }

    private void buildCycleDateRange(CycleDataDTO cycleData,
                                     CycleInfoVO cycleInfoVO) {
        CycleDateRange cycleDateRange = new CycleDateRange();
        cycleDateRange.setIndex(cycleData.getCycle_index());
        cycleDateRange.setStartDate(cycleData.getDate_period_start());
        cycleDateRange.setEndDate(LocalDateUtil.plusDay(cycleData.getDate_period_start(),
                cycleData.getLen_cycle() - 1, DatePatternConst.DATE_PATTERN));
        String datePeriodEnd = cycleData.getDate_period_end();
        cycleDateRange.setPeriodEnd(StringUtils.isBlank(datePeriodEnd) ? null
                : LocalDateUtil.plusDay(datePeriodEnd, -1, DatePatternConst.DATE_PATTERN));
        cycleDateRange.setCycleStatus(cycleData.getCycle_status());
        cycleInfoVO.getCycleRange().add(cycleDateRange);
    }

    private void buildOvuDate(CycleDataDTO cycleData, CycleInfoVO cycleInfoVO) {
        String dateOvulation = cycleData.getDate_ovulation();
        if (StringUtils.isNotBlank(dateOvulation)) {
            OvuData ovuData = new OvuData();
            ovuData.setDate(dateOvulation);
            ovuData.setType(cycleData.getOvulation_type());
            cycleInfoVO.getOvu().add(ovuData);
        }
    }

    private void buildCustomOvuDate(Long userId, CycleInfoVO cycleInfoVO) {
        AppOvulationManualEntity ovulationManualEntity = appOvulationManualDAO.getByUserId(userId);
        if (Objects.isNull(ovulationManualEntity)) {
            return;
        }

        List<OvuData> ovuCustomList = cycleInfoVO.getOvuCustom();
        List<String> ovuCustomStringList = JsonUtil.toArray(ovulationManualEntity.getOvuData(), String.class);
        for (String ovuDate : ovuCustomStringList) {
            OvuData ovuData = new OvuData();
            ovuData.setDate(ovuDate);
            ovuData.setType(OvulationTypeEnum.CUSTOM.getCode());
            ovuCustomList.add(ovuData);
        }
    }

    private void buildFw(CycleDataDTO cycleData, CycleInfoVO cycleInfoVO) {
        List<String> pFwList = cycleInfoVO.getPFw();
        List<String> fwList = cycleInfoVO.getFw();

        String fw = cycleData.getDate_FW_start();
        String fwEnd = cycleData.getDate_FW_end();

        if (StringUtils.isBlank(fw)) {
            return;
        }

        Integer ovulationType = cycleData.getOvulation_type();
        while (!fw.equals(fwEnd)) {
            if (Objects.equals(OvulationTypeEnum.PREDICTED.getCode(), ovulationType)
                    || Objects.equals(OvulationTypeEnum.PREDICTED_CONFIRMED.getCode(), ovulationType)) {
                pFwList.add(fw);
            } else {
                fwList.add(fw);
            }
            fw = LocalDateUtil.plusDay(fw, 1, DatePatternConst.DATE_PATTERN);
        }
    }
}
