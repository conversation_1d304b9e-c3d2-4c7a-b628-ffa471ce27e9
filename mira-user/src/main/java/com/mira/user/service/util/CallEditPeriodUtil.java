package com.mira.user.service.util;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.LastCycleFlagEnum;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.AgeUtil;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * call edit period util
 *
 * <AUTHOR>
 */
public class CallEditPeriodUtil {
    public static UserPeriodParamDTO buildUserPeriodParamDTO(LoginUserInfoDTO loginUserInfoDTO,
                                                             AppUserPeriodEntity appUserPeriodEntity, List<UserPeriodDataDTO> userPeriodDataDTOS) {
        UserPeriodParamDTO userPeriodParamDTO = new UserPeriodParamDTO();
        BeanUtil.copyProperties(appUserPeriodEntity, userPeriodParamDTO);

        userPeriodParamDTO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        userPeriodParamDTO.setIsOft(loginUserInfoDTO.getIsOft());
        userPeriodParamDTO.setConditions(loginUserInfoDTO.getConditions());
        userPeriodParamDTO.setAge(AgeUtil.calculateAge(loginUserInfoDTO.getBirthYear(), loginUserInfoDTO.getBirthMonth(), loginUserInfoDTO.getBirthDay()));
        userPeriodParamDTO.setAvgLenPeriod(appUserPeriodEntity.getAvgLenPeriod());
        userPeriodParamDTO.setAvgLenCycle(Objects.equals(1, loginUserInfoDTO.getIrregularCycle()) ? 30 : appUserPeriodEntity.getAvgLenCycle());
        userPeriodParamDTO.setUserPeriodDataDTOS(userPeriodDataDTOS);
        userPeriodParamDTO.setTrackingMenopause(loginUserInfoDTO.getTrackingMenopause());
        userPeriodParamDTO.setTrackingMenopauseDate(loginUserInfoDTO.getTrackingMenopauseDate());

        return userPeriodParamDTO;
    }

    /**
     * onboarding 流程专用
     */
    public static UserPeriodParamDTO buildUserNoPeriodParamDTO(Integer goalStatus, AppUserInfoEntity userInfoEntity,
                                                               List<UserPeriodDataDTO> userPeriodDataDTOS) {
        UserPeriodParamDTO userPeriodParamDTO = new UserPeriodParamDTO();
        userPeriodParamDTO.setUserId(userInfoEntity.getUserId());
        userPeriodParamDTO.setUserMode(goalStatus);
        userPeriodParamDTO.setPeriodFlag(-1);
        userPeriodParamDTO.setCycleFlag(-1);
        userPeriodParamDTO.setAge(AgeUtil.calculateAge(userInfoEntity.getBirthYear(), userInfoEntity.getBirthMonth(), userInfoEntity.getBirthOfDay()));
        userPeriodParamDTO.setConditions(userInfoEntity.getConditions());
        userPeriodParamDTO.setUserPeriodDataDTOS(userPeriodDataDTOS);

        return userPeriodParamDTO;
    }

    public static PrepareEditPeriodDTO buildPrepareEditPeriodDTO(String timeZone, String email, String periods,
                                                                 UserPeriodParamDTO userPeriodParamDTO) {
        PrepareEditPeriodDTO prepareEditPeriodDTO = new PrepareEditPeriodDTO();
        prepareEditPeriodDTO.setTimeZone(timeZone);
        prepareEditPeriodDTO.setEmail(email);
        prepareEditPeriodDTO.setLastCycleFlag(LastCycleFlagEnum.ZERO.getFlag());
        prepareEditPeriodDTO.setPeriods(periods);
        prepareEditPeriodDTO.setUserPeriodParamDTO(userPeriodParamDTO);

        return prepareEditPeriodDTO;
    }

    /**
     * onboarding 流程专用
     */
    public static PrepareEditPeriodDTO buildPrepareEditNoPeriodDTO(String timeZone, String email,
                                                                   UserPeriodParamDTO userPeriodParamDTO) {
        PrepareEditPeriodDTO prepareEditPeriodDTO = new PrepareEditPeriodDTO();
        prepareEditPeriodDTO.setTimeZone(timeZone);
        prepareEditPeriodDTO.setEmail(email);
        prepareEditPeriodDTO.setLastCycleFlag(LastCycleFlagEnum.THREE.getFlag());
        prepareEditPeriodDTO.setUserPeriodParamDTO(userPeriodParamDTO);
        prepareEditPeriodDTO.setNoPeriod(true);

        return prepareEditPeriodDTO;
    }

    public static List<UserPeriodDataDTO> buildUserPeriodDataDTOS(String timeZone, List<Long> periodList, String cutPoints, Integer avgLenPeriod) {
        List<String> periodStrList = PeriodUtil.periodsLong2String(periodList, timeZone);
        List<UserPeriodDataDTO> userPeriodDataDTOS;
        if (StringUtils.isBlank(cutPoints)) {
            userPeriodDataDTOS = PeriodUtil.buildPeriodList(avgLenPeriod, periodStrList);
        } else {
            List<String> cutPointList = JsonUtil.toArray(cutPoints, String.class);
            userPeriodDataDTOS = PeriodUtil.buildPeriodListWithInterval(avgLenPeriod, periodStrList, cutPointList);
        }
        return userPeriodDataDTOS;
    }

    /**
     * onboarding 流程专用
     */
    public static List<UserPeriodDataDTO> buildUserNoPeriodDataDTOS(String timeZone,
                                                                    AppUserInfoEntity appUserInfoEntity) {
        List<UserPeriodDataDTO> userPeriodDataDTOS = new ArrayList<>();
        UserPeriodDataDTO userPeriodDataDTO = new UserPeriodDataDTO();
        userPeriodDataDTO.setFlag(LastCycleFlagEnum.THREE.getFlag());

        // 取用户注册的时间
        String periodStart = ZoneDateUtil.format(timeZone, appUserInfoEntity.getCreateTime(), DatePatternConst.DATE_PATTERN);
        userPeriodDataDTO.setDate_period_start(periodStart);

        userPeriodDataDTOS.add(userPeriodDataDTO);

        return userPeriodDataDTOS;
    }
}
