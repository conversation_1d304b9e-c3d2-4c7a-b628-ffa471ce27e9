package com.mira.user.service.notification.v5;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.message.dto.NotificationRateApiDTO;
import com.mira.api.message.dto.PageNotificationRecordDTO;
import com.mira.api.message.dto.QueryNotificationDTO;
import com.mira.api.message.dto.SysNotificationRecordDTO;
import com.mira.api.message.enums.NotificationAggregateTypeEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.request.PageDTO;
import com.mira.mybatis.response.PageResult;
import com.mira.user.controller.vo.user.NotificationRecordVO;
import com.mira.user.controller.vo.user.NotificationTypeVO;
import com.mira.user.dal.dao.AppPregnantModeInfoV2DAO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.UserPaywallDAO;
import com.mira.user.dal.entity.AppPregnantModeInfoV2Entity;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.UserPaywallEntity;
import com.mira.user.dto.notification.NotificationDeleteDTO;
import com.mira.user.dto.notification.NotificationPageQueryDTO;
import com.mira.user.dto.notification.NotificationRateDTO;
import com.mira.user.exception.UserException;
import com.mira.user.service.manager.AlgorithmCallParamManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * notification service
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("all")
@Service("v5NotificationService")
public class V5NotificationServiceImpl implements IV5NotificationService {
    @Resource
    private UserPaywallDAO userPaywallDAO;
    @Resource
    private AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    @Resource
    private AlgorithmCallParamManager algorithmCallParamManager;
    @Resource
    private IMessageProvider messageProvicer;
    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;

    @Override
    public List<NotificationTypeVO> aggregateTypeList() {
        List<NotificationTypeVO> resultVO = new LinkedList<>();
        for (NotificationAggregateTypeEnum notificationAggregateTypeEnum : NotificationAggregateTypeEnum.values()) {
            NotificationTypeVO notificationTypeVO = new NotificationTypeVO();
            notificationTypeVO.setType(notificationAggregateTypeEnum.getAggregateType());
            notificationTypeVO.setName(notificationAggregateTypeEnum.getContent());
            resultVO.add(notificationTypeVO);
        }
        resultVO.sort(Comparator.comparingInt(NotificationTypeVO::getType));

        return resultVO;
    }

    @Override
    public PageResult<NotificationRecordVO> page(NotificationPageQueryDTO notificationPageQueryDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        // check
        Integer aggregateType = notificationPageQueryDTO.getAggregateType();
        Integer current = notificationPageQueryDTO.getCurrent();
        Integer size = notificationPageQueryDTO.getSize();
        NotificationAggregateTypeEnum aggregateTypeEnum = NotificationAggregateTypeEnum.get(aggregateType);
        if (Objects.isNull(aggregateTypeEnum)) {
            throw new UserException("aggregate type mismatch.");
        }
        PageDTO pageDTO = new PageDTO();
        pageDTO.setCurrent(current);
        pageDTO.setSize(size);

        switch (userTypeEnum) {
            case APP_USER:
                QueryNotificationDTO queryNotificationDTO = new QueryNotificationDTO();
                queryNotificationDTO.setUserId(loginInfo.getId());
                queryNotificationDTO.setAggregateType(aggregateType);
                queryNotificationDTO.setPageDTO(pageDTO);
                PageNotificationRecordDTO recordDTO = messageProvicer.getNotificationList(queryNotificationDTO).getData();
                if (CollectionUtils.isNotEmpty(recordDTO.getList())) {
                    List<NotificationRecordVO> voList = recordDTO.getList().stream()
                            .map(dto -> BeanUtil.toBean(dto, NotificationRecordVO.class))
                            .collect(Collectors.toList());
                    return new PageResult<>(voList, recordDTO.getTotal(), size, current);
                }
            case PARTNER_USER:
                return new PageResult<>();
        }

        return new PageResult<>();
    }

    @Override
    public NotificationRecordVO silent() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        SysNotificationRecordDTO recordDTO = messageProvicer.getSilent(userId).getData();
        if (recordDTO == null) {
            return null;
        }
        return BeanUtil.toBean(recordDTO, NotificationRecordVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRead(Long id) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        messageProvicer.updateRead(userId, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUnRead(Long id) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        messageProvicer.updateUnRead(userId, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public NotificationRecordVO delete(NotificationDeleteDTO notificationDeleteDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        // delete
        messageProvicer.delete(notificationDeleteDTO.getDeleteId());
        // get
        if (notificationDeleteDTO.getBottomRecordId() == null) {
            return null;
        }
        SysNotificationRecordDTO notificationRecordDTO = messageProvicer
                .supply(userId, notificationDeleteDTO.getAggregateType(), notificationDeleteDTO.getBottomRecordId())
                .getData();
        if (notificationRecordDTO == null) {
            return null;
        }
        return BeanUtil.toBean(notificationRecordDTO, NotificationRecordVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void paywall() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        long count = userPaywallDAO.countByUserId(userId);
        if (count == 0) {
            UserPaywallEntity userPaywallEntity = new UserPaywallEntity();
            userPaywallEntity.setUserId(userId);
            userPaywallEntity.setCreateTime(System.currentTimeMillis());
            userPaywallDAO.save(userPaywallEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enterNoPeriodMode() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();

        // 如果已经是no period模式，直接返回
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            return;
        }

        // whether pregnant
        AppPregnantModeInfoV2Entity pregnantModeInfoV2Entity = appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
        loginUserInfoDTO.setNoPeriodFlag(pregnantModeInfoV2Entity != null ? UserGoalEnum.TTC.getValue() : UserGoalEnum.CYCLE_TRACKING.getValue());

        // update user info
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        appUserInfoEntity.setNoPeriod(loginUserInfoDTO.getNoPeriodFlag());
        appUserInfoDAO.updateById(appUserInfoEntity);

        algorithmCallParamManager.callAlgorithm(userId, loginUserInfoDTO, AlgorithmRequestTypeEnum.EDIT_NO_PERIOD);
        log.info("user:{} enter no period mode by notification");
    }

    @Override
    public void rate(NotificationRateDTO notificationRateDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        UserTypeEnum userTypeEnum = UserTypeEnum.get(loginInfo.getUserType());

        if (userTypeEnum == UserTypeEnum.APP_USER) {
            NotificationRateApiDTO notificationRateApiDTO = new NotificationRateApiDTO();
            notificationRateApiDTO.setUserId(loginInfo.getId());
            notificationRateApiDTO.setType(notificationRateDTO.getType());
            notificationRateApiDTO.setScore(notificationRateDTO.getScore());
            messageProvicer.rateByNotification(notificationRateApiDTO);
        }
    }
}
