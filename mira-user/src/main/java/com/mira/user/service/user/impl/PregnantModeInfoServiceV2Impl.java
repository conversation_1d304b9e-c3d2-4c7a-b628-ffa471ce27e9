package com.mira.user.service.user.impl;

import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.controller.vo.user.PregnantModeInfoVO;
import com.mira.user.dal.dao.AppPregnantModeInfoV2DAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppPregnantModeInfoV2Entity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.dto.info.UserEditPregnantModeInfoDTO;
import com.mira.user.dto.info.UserRemovePregnantyDTO;
import com.mira.user.dto.schedule.GoalTestingScheduleDTO;
import com.mira.user.enums.user.EditGoalFlagEnum;
import com.mira.user.exception.UserException;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.user.IPregnantModeInfoV2Service;
import com.mira.user.service.user.IUserInfoService;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-01
 **/
@Slf4j
@Service
public class PregnantModeInfoServiceV2Impl implements IPregnantModeInfoV2Service {
    @Resource
    private AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;

    @Resource
    private IUserInfoService userInfoService;

    @Resource
    private ISsoProvider ssoProvider;
    @Resource
    private AlgorithmCallManager algorithmCallManager;

    @Override
    public PregnantModeInfoVO pregnantModeInfo() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        PregnantModeInfoVO pregnantModeInfoVO = new PregnantModeInfoVO();

        AppPregnantModeInfoV2Entity appPregnantModeInfoV2Entity =
                appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
        if (appPregnantModeInfoV2Entity == null) {
            String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
            pregnantModeInfoVO.setLastPeriodDate(lastPeriodDate);
            return pregnantModeInfoVO;
        }
        BeanUtils.copyProperties(appPregnantModeInfoV2Entity, pregnantModeInfoVO);
        // 最后一个实周期的开始日
        String existLastPeriodDate = appPregnantModeInfoV2Entity.getLastPeriodDate();
        String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
        pregnantModeInfoVO.setLastPeriodDate(lastPeriodDate);

        if (StringUtils.isNotBlank(existLastPeriodDate)
                && StringUtils.isNotBlank(lastPeriodDate)
                && !existLastPeriodDate.equals(lastPeriodDate)
        ) {
            appPregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appPregnantModeInfoV2Entity);
            appPregnantModeInfoV2DAO.updateById(appPregnantModeInfoV2Entity);
        }
        return pregnantModeInfoVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPregnantModeInfo(UserEditPregnantModeInfoDTO userEditPregnantModeInfoDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        String lastPeriodDate = userEditPregnantModeInfoDTO.getLastPeriodDate();
        String conceptionDate = userEditPregnantModeInfoDTO.getConceptionDate();
        String dueDate = userEditPregnantModeInfoDTO.getDueDate();
        Integer numberOfChildren = userEditPregnantModeInfoDTO.getNumberOfChildren();
        // check conceptionDate && dueDate

        //date_period_start <= conception_date < due_date  < next date_period_start ( if next date_period_start exists)
        if (StringUtils.isNotBlank(lastPeriodDate) && StringUtils.isNotBlank(conceptionDate)) {
            int minusToDay = LocalDateUtil.minusToDay(conceptionDate, lastPeriodDate);
            if (minusToDay < 0) {
                throw new UserException("Conception date should later than last period date.");
            }
            if (minusToDay > 10 * 7) {
                throw new UserException("Conception date up to 10 weeks from last period date.");
            }
        }

        // 38～42 周
        if (StringUtils.isNotBlank(lastPeriodDate) && StringUtils.isNotBlank(dueDate)) {
            int minusToDay = LocalDateUtil.minusToDay(dueDate, lastPeriodDate);
            if (minusToDay <= 0) {
                throw new UserException("Due date should later than conception date.");
            }
            if (minusToDay > 42 * 7 || minusToDay < 38 * 7) {
                throw new UserException(BizCodeEnum.PREGNANT_DUE_DATE_TOO_CLOSE);
            }
        }

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);

        AppPregnantModeInfoV2Entity pregnantModeInfoV2Entity =
                appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
        if (pregnantModeInfoV2Entity == null) {
            pregnantModeInfoV2Entity = new AppPregnantModeInfoV2Entity();
            pregnantModeInfoV2Entity.setUserId(userId);

            pregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
            pregnantModeInfoV2Entity.setConceptionDate(conceptionDate);
            pregnantModeInfoV2Entity.setDueDate(dueDate);
            pregnantModeInfoV2Entity.setNumberOfChildren(numberOfChildren);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, pregnantModeInfoV2Entity);
            appPregnantModeInfoV2DAO.save(pregnantModeInfoV2Entity);
        } else {
            pregnantModeInfoV2Entity.setLastPeriodDate(lastPeriodDate);
            pregnantModeInfoV2Entity.setConceptionDate(conceptionDate);
            pregnantModeInfoV2Entity.setDueDate(dueDate);
            pregnantModeInfoV2Entity.setNumberOfChildren(numberOfChildren);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, pregnantModeInfoV2Entity);

            //todo: 如果有退出怀孕的记录怎么办？

            appPregnantModeInfoV2DAO.updateById(pregnantModeInfoV2Entity);
        }

        //需要基于怀孕模式编辑经期
        // 构建经期信息
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, null);
        // 调用算法
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.CHANGE_PREGNANT_MODE_INFO);
    }

    @Override
    //    @Transactional(rollbackFor = Exception.class)
    public void removePregnanty(UserRemovePregnantyDTO userRemovePregnantyDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AppPregnantModeInfoV2Entity pregnantModeInfoV2Entity =
                appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
        if (pregnantModeInfoV2Entity == null) {
            return;
        }
        pregnantModeInfoV2Entity.setIsEnd(1);
        pregnantModeInfoV2Entity.setEndReason(userRemovePregnantyDTO.getEndReason());
        pregnantModeInfoV2Entity.setDeliveryDate(userRemovePregnantyDTO.getDeliveryDate());
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, pregnantModeInfoV2Entity);

        String dueDate = pregnantModeInfoV2Entity.getDueDate();
        if (StringUtils.isBlank(dueDate)) {
            String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            pregnantModeInfoV2Entity.setDueDate(today);
        }

        appPregnantModeInfoV2DAO.updateById(pregnantModeInfoV2Entity);

        //需要切换到怀孕模式之前的那个模式下并编辑经期
        GoalTestingScheduleDTO goalTestingScheduleDTO = userRemovePregnantyDTO.getGoalTestingScheduleDTO();
        userInfoService.changeModeLastMode(EditGoalFlagEnum.REMOVE_PREGNANT_FROM_PREGNANT_SETTING, goalTestingScheduleDTO);
        //所有结束怀孕模式都需要将怀孕历史存档？ 这个方式下不会删除数据，所以不需要了
    }
}
