package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.bbt.BBTDataDTO;
import com.mira.api.bluetooth.dto.bbt.BBTInfoDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.dto.period.AlgorithmLongerPeriodDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.dto.report.AlgorithmReportDataDTO;
import com.mira.api.bluetooth.dto.report.ReportReturnDTO;
import com.mira.api.bluetooth.dto.tips.AlgorithmGetTipsDTO;
import com.mira.api.bluetooth.dto.tips.GetTipsReturnDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.BBTModeErrorCodeEnum;
import com.mira.api.bluetooth.enums.LastCycleFlagEnum;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.diary.DateSymptomDTO;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.StringListUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.dal.dao.AppOvulationManualDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.dao.UserPeriodEditNoteDAO;
import com.mira.user.dal.dao.UserProductTrialDAO;
import com.mira.user.dal.entity.*;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 算法请求调用处理层
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlgorithmCallManager {
    @Resource
    private UserProductTrialDAO userProductTrialDAO;
    @Resource
    private AppOvulationManualDAO appOvulationManualDAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private UserPeriodEditNoteDAO userPeriodEditNoteDAO;

    @Resource
    private IAlgorithmProvider algorithmProvider;
    @Resource
    private IBluetoothProvider bluetoothProvider;

    @Resource
    private SysDictProperties sysDictProperties;
    @Resource
    private RedisComponent redisComponent;

    private boolean isQcUser(String email) {
        String qcEmails = sysDictProperties.getQcEmails();
        if (qcEmails.contains(email)) {
            log.info("qc user:{}, no call algorithm", email);
            return true;
        }
        return false;
    }

    private boolean checkIdempotent(Long userId, String lockKey) {
        return redisComponent.setIfAbent(lockKey, userId, 3, TimeUnit.SECONDS);
    }

    /**
     * 编辑经期
     */
    public Integer editPeriod(PrepareEditPeriodDTO prepareEditPeriodDTO, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        try {
            if (isQcUser(prepareEditPeriodDTO.getEmail())) {
                return -1;
            }
        } catch (Exception e) {
            log.error("check qc user error");
        }

        // idempotent
        Long userId = prepareEditPeriodDTO.getUserPeriodParamDTO().getUserId();
        String lockKey = RedisCacheKeyConst.EDIT_PERIOD_IDEMPOTENT + userId;
        if (!checkIdempotent(userId, lockKey)) {
            return -1;
        }

        AlgorithmEditPeriodDTO algorithmEditPeriodDTO = getAlgorithmEditPeriodDTO(prepareEditPeriodDTO, algorithmRequestTypeEnum);
        return algorithmProvider.algorithmEditPeriod(algorithmEditPeriodDTO).getData();
    }

    public AlgorithmEditPeriodDTO getAlgorithmEditPeriodDTO(PrepareEditPeriodDTO prepareEditPeriodDTO, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        String timeZone = prepareEditPeriodDTO.getTimeZone();
        Integer lastCycleFlag = prepareEditPeriodDTO.getLastCycleFlag();
        UserPeriodParamDTO userPeriodParamDTO = prepareEditPeriodDTO.getUserPeriodParamDTO();
        UserProductTrialEntity userProductTrial = userProductTrialDAO.getByEmail(prepareEditPeriodDTO.getEmail());

        List<UserPeriodDataDTO> userPeriodDataDTOS = userPeriodParamDTO.getUserPeriodDataDTOS();
        if (CollectionUtils.isEmpty(userPeriodDataDTOS)) {
            List<Long> dbPeriodList = StringListUtil.strToLongList(prepareEditPeriodDTO.getPeriods(), ",");
            userPeriodDataDTOS = CallEditPeriodUtil.buildUserPeriodDataDTOS(timeZone, dbPeriodList,
                    userPeriodParamDTO.getCutPoints(), userPeriodParamDTO.getAvgLenPeriod());
        }

        // handle manual ovu
        AppOvulationManualEntity ovulationManualEntity = appOvulationManualDAO.getByUserId(userPeriodParamDTO.getUserId());
        if (ovulationManualEntity != null) {
            String ovuData = ovulationManualEntity.getOvuData();
            if (StringUtils.isNotBlank(ovuData)) {
                List<String> manualOvuList = JsonUtil.toArray(ovuData, String.class);
                if (prepareEditPeriodDTO.getNoPeriod()) {
                    handleNoPeriodManualOvuList(manualOvuList, userPeriodDataDTOS);
                } else {
                    handleManualOvuList(manualOvuList, userPeriodDataDTOS);
                }
            }
        }

        // handle predicted period
        handlePredictedPeriod(userPeriodParamDTO.getUserId(), userPeriodDataDTOS);

        AlgorithmEditPeriodDTO algorithmEditPeriodDTO = new AlgorithmEditPeriodDTO();
        algorithmEditPeriodDTO.setTimeZone(timeZone);
        algorithmEditPeriodDTO.setTrialFlag(userProductTrial != null ? userProductTrial.getFlag() : null);
        algorithmEditPeriodDTO.setLastCycleFlag(lastCycleFlag);
        algorithmEditPeriodDTO.setUserPeriodParam(userPeriodParamDTO);
        algorithmEditPeriodDTO.setUserPeriodDataList(userPeriodDataDTOS);
        algorithmEditPeriodDTO.setAlgorithmRequestTypeEnum(algorithmRequestTypeEnum);
        return algorithmEditPeriodDTO;
    }

    private void handleManualOvuList(List<String> manualOvuList, List<UserPeriodDataDTO> userPeriodDataDTOS) {
        int userPeriodDataSize = userPeriodDataDTOS.size();
        Iterator<String> manualOvuIterator = manualOvuList.iterator();
        while (manualOvuIterator.hasNext()) {
            String manualOvuDate = manualOvuIterator.next();
            for (int i = userPeriodDataSize - 1; i >= 0; i--) {
                UserPeriodDataDTO userPeriodDataDTO = userPeriodDataDTOS.get(i);
                String start = userPeriodDataDTO.getDate_period_start();
                if (LocalDateUtil.minusToDay(manualOvuDate, start) >= 0) {
                    if (StringUtils.isNotBlank(userPeriodDataDTO.getManual_ovulation())) {
                        manualOvuIterator.remove();
                        break;
                    }
                    userPeriodDataDTO.setManual_ovulation(manualOvuDate);
                    break;
                }
            }
        }
    }

    private void handleNoPeriodManualOvuList(List<String> manualOvuList, List<UserPeriodDataDTO> userPeriodDataDTOS) {
        int userPeriodDataSize = userPeriodDataDTOS.size();
        for (String manualOvuDate : manualOvuList) {
            for (int i = userPeriodDataSize - 1; i >= 0; i--) {
                UserPeriodDataDTO userPeriodDataDTO = userPeriodDataDTOS.get(i);
                String start = userPeriodDataDTO.getDate_period_start();
                if (LocalDateUtil.minusToDay(manualOvuDate, start) >= 0) {
                    userPeriodDataDTO.getManual_ovulation_multi().add(manualOvuDate);
                    break;
                }
            }
        }
    }

    private void handlePredictedPeriod(Long userId, List<UserPeriodDataDTO> userPeriodDataDTOS) {
        // query user period edit note
        List<UserPeriodEditNoteEntity> userPeriodEditNoteEntities = userPeriodEditNoteDAO.listByUserId(userId);
        if (CollectionUtils.isNotEmpty(userPeriodEditNoteEntities)) {
            Map<String, UserPeriodEditNoteEntity> periodEditNoteMap = userPeriodEditNoteEntities.stream()
                    .collect(Collectors.toMap(UserPeriodEditNoteEntity::getPreviousStart, Function.identity()));
            int size = userPeriodDataDTOS.size();
            for (int index = 0; index < size - 1; index++) {
                UserPeriodDataDTO userPeriodDataDTO = userPeriodDataDTOS.get(index);
                UserPeriodEditNoteEntity periodEditNote = periodEditNoteMap.get(userPeriodDataDTO.getDate_period_start());
                if (periodEditNote != null) {
                    UserPeriodDataDTO nextUserPeriodData = userPeriodDataDTOS.get(index + 1);
                    userPeriodDataDTO.setFlag(LastCycleFlagEnum.ONE.getFlag());
                    userPeriodDataDTO.setLen_cycle(LocalDateUtil.minusToDay(nextUserPeriodData.getDate_period_start(),
                            userPeriodDataDTO.getDate_period_start()));
                }
            }
        }
    }

    /**
     * 编辑经期，经期数据不变
     *
     * @param loginUserInfoDTO 用户详情
     */
    public void editDBPeriod(LoginUserInfoDTO loginUserInfoDTO, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        Long userId = loginUserInfoDTO.getUserId();
        String timeZone = loginUserInfoDTO.getTimeZone();

        AppUserPeriodEntity userPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (Objects.isNull(userPeriodEntity)) {
            return;
        }

        List<Long> dbPeriodList = StringListUtil.strToLongList(userPeriodEntity.getPeriods(), ",");
        List<UserPeriodDataDTO> userPeriodDataDTOS = CallEditPeriodUtil.buildUserPeriodDataDTOS(timeZone, dbPeriodList,
                userPeriodEntity.getCutPoints(), userPeriodEntity.getAvgLenPeriod());
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO,
                userPeriodEntity, userPeriodDataDTOS);
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone, loginUserInfoDTO.getEmail(),
                userPeriodEntity.getPeriods(), userPeriodParamDTO);

        editPeriod(prepareEditPeriodDTO, algorithmRequestTypeEnum);
    }

    /**
     * 编辑经期，不入库
     */
    public AlgorithmResultDTO editPeriodNotUpdateDB(PrepareEditPeriodDTO prepareEditPeriodDTO, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        try {
            if (isQcUser(prepareEditPeriodDTO.getEmail())) {
                return null;
            }
        } catch (Exception e) {
            //
        }

        // idempotent
        Long userId = prepareEditPeriodDTO.getUserPeriodParamDTO().getUserId();
        String lockKey = RedisCacheKeyConst.EDIT_PERIOD_NOT_UPDATE_DB_IDEMPOTENT + userId;
        if (!checkIdempotent(userId, lockKey)) {
            return null;
        }

        AlgorithmEditPeriodDTO algorithmEditPeriodDTO = getAlgorithmEditPeriodDTO(prepareEditPeriodDTO, algorithmRequestTypeEnum);
        return algorithmProvider.algorithmEditPeriodNotUpdateDB(algorithmEditPeriodDTO).getData();
    }

    /**
     * 长周期
     */
    public void lognerPeriod(AppUserPeriodEntity appUserPeriodEntity, LoginUserInfoDTO loginUserInfoDTO) {
        try {
            if (isQcUser(loginUserInfoDTO.getEmail())) {
                return;
            }
        } catch (Exception e) {
            log.error("check qc user error");
        }

        AlgorithmLongerPeriodDTO algorithmLongerPeriodDTO = new AlgorithmLongerPeriodDTO();
        algorithmLongerPeriodDTO.setUserId(appUserPeriodEntity.getUserId());
        algorithmLongerPeriodDTO.setTimeZone(appUserPeriodEntity.getTimeZone());
        algorithmLongerPeriodDTO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        algorithmLongerPeriodDTO.setCycleFlag(appUserPeriodEntity.getCycleFlag());
        algorithmLongerPeriodDTO.setPeriodFlag(appUserPeriodEntity.getPeriodFlag());
        algorithmProvider.algorithmLongerPeriod(algorithmLongerPeriodDTO);
    }

    /**
     * tips
     */
    public GetTipsReturnDTO tips(AppUserPeriodEntity appUserPeriodEntity, List<AppUserTemperatureEntity> temperatureEntityList,
                                 LoginUserInfoDTO loginUserInfoDTO, Integer trialFlag, Integer tipsType) {
        try {
            if (isQcUser(loginUserInfoDTO.getEmail())) {
                GetTipsReturnDTO getTipsReturnDTO = new GetTipsReturnDTO();
                getTipsReturnDTO.setUser_id(appUserPeriodEntity.getUserId());
                getTipsReturnDTO.setTips(new ArrayList<>());
                return getTipsReturnDTO;
            }
        } catch (Exception e) {
            log.error("check qc user error");
        }

        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, null);
        userPeriodParamDTO.setPeriodFlag(appUserPeriodEntity.getPeriodFlag());

        AlgorithmGetTipsDTO algorithmGetTipsDTO = new AlgorithmGetTipsDTO();
        algorithmGetTipsDTO.setTimeZone(appUserPeriodEntity.getTimeZone());
        algorithmGetTipsDTO.setTrialFlag(trialFlag);
        algorithmGetTipsDTO.setUserPeriodParam(userPeriodParamDTO);

        BBTInfoDTO bbtInfoDTO = bluetoothProvider.getBbtInfo(loginUserInfoDTO.getUserId()).getData();
        algorithmGetTipsDTO.setBindBbt(bbtInfoDTO == null ? 0 : bbtInfoDTO.getBind());

        algorithmGetTipsDTO.setBbt(buildBBTData(temperatureEntityList));
        algorithmGetTipsDTO.setRemindFlag(loginUserInfoDTO.getRemindFlag());
        algorithmGetTipsDTO.setTestingScheduleFlag(loginUserInfoDTO.getTestingScheduleFlag());
        algorithmGetTipsDTO.setAlgorithmRequestTypeEnum(AlgorithmRequestTypeEnum.GET_TIPS);
        algorithmGetTipsDTO.setTipsType(tipsType);
        return algorithmProvider.algorithmTipsData(algorithmGetTipsDTO).getData();
    }

    private List<BBTDataDTO> buildBBTData(List<AppUserTemperatureEntity> temperatureEntityList) {
        return temperatureEntityList.stream()
                .filter(temperature ->
                        BBTModeErrorCodeEnum.NORMAL.getCode().equals(temperature.getModeError())
                                && (temperature.getTempF() != null && !Objects.equals(temperature.getTempF(), BigDecimal.ZERO))
                                && temperature.getAutoFlag() == 0)
                .map(temperature -> {
                    BBTDataDTO bbtDataDTO = new BBTDataDTO();
                    bbtDataDTO.setId(temperature.getId());
                    bbtDataDTO.setTime(temperature.getTempTime());
                    bbtDataDTO.setTemperature(temperature.getTempF().toString());
                    return bbtDataDTO;
                }).collect(Collectors.toList());
    }

    /**
     * report
     */
    public ReportReturnDTO report(AppUserPeriodEntity appUserPeriodEntity,
                                  LoginUserInfoDTO loginUserInfoDTO,
                                  List<CycleDataDTO> selectedCycles,
                                  List<HormoneDTO> analysisHormoneDatas,
                                  List<DateSymptomDTO> selectUserSymptomDTOS) {
        try {
            if (isQcUser(loginUserInfoDTO.getEmail())) {
                return null;
            }
        } catch (Exception e) {
            log.error("check qc user error");
        }

        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, null);
        userPeriodParamDTO.setPeriodFlag(appUserPeriodEntity.getPeriodFlag());

        AlgorithmReportDataDTO algorithmReportDataDTO = new AlgorithmReportDataDTO();
        algorithmReportDataDTO.setUserPeriodParam(userPeriodParamDTO);
        algorithmReportDataDTO.setTimeZone(appUserPeriodEntity.getTimeZone());

        UserProductTrialEntity userProductTrial = userProductTrialDAO.getByEmail(loginUserInfoDTO.getEmail());
        algorithmReportDataDTO.setTrialFlag(userProductTrial != null ? userProductTrial.getFlag() : null);

        algorithmReportDataDTO.setCycleDataDTOS(selectedCycles);
        algorithmReportDataDTO.setHormoneDataDTOS(analysisHormoneDatas);
        algorithmReportDataDTO.setListDateSymptomDTO(JsonUtil.toJson(selectUserSymptomDTOS));
        return algorithmProvider.algorithmReportData(algorithmReportDataDTO).getData();
    }
}
