package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.StringListUtil;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2025-01-09
 **/
@Slf4j
@Component
public class AlgorithmCallParamManager {
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private AlgorithmCallManager algorithmCallManager;

    public void callAlgorithm(Long userId, LoginUserInfoDTO loginUserInfoDTO,
                              AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();

        // update app user period
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        appUserPeriodEntity.setFresh(1);
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        // update app user info
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        appUserInfoEntity.setOnboardingStatus(2);
        // delete cache
        cacheManager.deleteUserDetailCache(userId);

        // if no period
        if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
            String timeZone = loginInfo.getTimeZone();
            // 调用算法
            List<UserPeriodDataDTO> userPeriodDataDTOS = CallEditPeriodUtil.buildUserNoPeriodDataDTOS(timeZone, appUserInfoEntity);
            UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserNoPeriodParamDTO(appUserInfoEntity.getGoalStatus(), appUserInfoEntity, userPeriodDataDTOS);
            PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditNoPeriodDTO(timeZone, loginInfo.getUsername(), userPeriodParamDTO);
            algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EDIT_NO_PERIOD);
            return;
        }

        // build user period data
        List<Long> dbPeriodList = StringListUtil.strToLongList(appUserPeriodEntity.getPeriods(), ",");
        List<UserPeriodDataDTO> userPeriodDataDTOList = CallEditPeriodUtil.buildUserPeriodDataDTOS(loginInfo.getTimeZone(),
                dbPeriodList, appUserPeriodEntity.getCutPoints(), appUserPeriodEntity.getAvgLenPeriod());
        // save last period data, context
        ContextHolder.put("lastPeriodData", CollectionUtils.isEmpty(userPeriodDataDTOList) ? null : userPeriodDataDTOList.get(userPeriodDataDTOList.size() - 1));
        // call algorithm
        algorithmCallManager.editDBPeriod(loginUserInfoDTO, algorithmRequestTypeEnum);
    }
}
