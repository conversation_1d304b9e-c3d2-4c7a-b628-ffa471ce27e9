package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppDataManualEntity;
import com.mira.user.dal.mapper.AppDataManualMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_data_manual DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppDataManualDAO extends ServiceImpl<AppDataManualMapper, AppDataManualEntity> {

    public List<AppDataManualEntity> listPendingDataByUserId(Long userId) {
        return list(Wrappers.<AppDataManualEntity>lambdaQuery()
                            .eq(AppDataManualEntity::getStatus, 0)
                            .eq(AppDataManualEntity::getUserId, userId));
    }

    public List<AppDataManualEntity> listByUserIdAndCompleteTime(Long userId,String completeTime) {
        return list(Wrappers.<AppDataManualEntity>lambdaQuery()
                            .eq(AppDataManualEntity::getUserId, userId)
                            .eq(AppDataManualEntity::getCompleteTime, completeTime)
        );
    }

    public List<AppDataManualEntity> listAllPendingData() {
        return list(Wrappers.<AppDataManualEntity>lambdaQuery()
                            .eq(AppDataManualEntity::getStatus, 0));
    }
}
