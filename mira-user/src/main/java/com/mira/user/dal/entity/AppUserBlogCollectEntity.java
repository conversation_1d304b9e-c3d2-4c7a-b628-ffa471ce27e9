package com.mira.user.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户博客收藏表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("app_user_blog_collect")
public class AppUserBlogCollectEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 博客id
     */
    private Long blogId;

    /**
     * 是否收藏:1收藏；0取消收藏
     */
    private Integer collect;
}
