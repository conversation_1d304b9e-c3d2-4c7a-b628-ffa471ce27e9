package com.mira.user.handler.event;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.user.dal.dao.AppPregnantModeInfoV2DAO;
import com.mira.user.dal.dao.AppUserInfoDAO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import com.mira.user.dal.entity.AppPregnantModeInfoV2Entity;
import com.mira.user.dal.entity.AppUserInfoEntity;
import com.mira.user.dal.entity.AppUserPeriodEntity;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.period.IUserPeriodService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * period listener
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PeriodListener {
    @Resource
    private IUserPeriodService userPeriodService;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;
    @Resource
    private AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IMessageProvider messageProvider;

    @EventListener(EditPeriodEvent.class)
    public void editPeriodEvent(EditPeriodEvent event) {
        List<String> periods = (List<String>) event.getSource();
        userPeriodService.updatePeriod(periods);
    }

//    @EventListener(EnterHomePageEvent.class)
//    public void enterHomePageEvent(EnterHomePageEvent event) {
//        Long userId = (Long) event.getSource();
//        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
//        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
//
//        // whether enter no period
//        whetherEnterNoPeriod(userId, today, timeZone);
//        // user complete first cycle
//        userCompleteFirstCycle(userId, today, timeZone);
//    }

    private void whetherEnterNoPeriod(Long userId, String today, String timeZone) {
        CompletableFuture.runAsync(() -> {
            // check mark
            if (redisComponent.exists(RedisCacheKeyConst.NO_PERIOD_MARK_NOTIFICATION + userId)) {
                return;
            }
            // user have logged period before
            AppUserPeriodEntity userPeriodEntity = appUserPeriodDAO.getByUserId(userId);
            boolean checkUserPeriod = userPeriodEntity == null
                    || StringUtils.isBlank(userPeriodEntity.getPeriodData())
                    || "[]".equals(userPeriodEntity.getPeriodData());
            if (checkUserPeriod) {
                return;
            }
            // on current day there is > 150 days with no-logged period
            List<UserPeriodDataDTO> periods = JsonUtil.toArray(userPeriodEntity.getPeriodData(), UserPeriodDataDTO.class);
            UserPeriodDataDTO latestPeriod = periods.get(periods.size() - 1);
            int diff = LocalDateUtil.minusToDay(today, latestPeriod.getDate_period_start());
            if (diff <= 150) {
                return;
            }
            // user is not in pregnancy mode
            AppPregnantModeInfoV2Entity recentPregnantModeInfo = appPregnantModeInfoV2DAO.getRecentPregnantModeInfoByUserId(userId);
            if (recentPregnantModeInfo != null && recentPregnantModeInfo.getIsEnd() == 0) {
                return;
            }
            // user is not in no period
            AppUserInfoEntity userInfo = appUserInfoDAO.getByUserId(userId);
            if (NoPeriodGoalEnum.get(userInfo.getNoPeriod()) != null) {
                return;
            }
            // send notification
            String expireDay = LocalDateUtil.plusDay(today, 2, DatePatternConst.DATE_PATTERN) + " 23:59:59";
            sendNotification(userInfo, timeZone, expireDay, NotificationDefineEnum.NO_PERIOD.getDefineId());
            // mark, expire 1 month
            redisComponent.setEx(RedisCacheKeyConst.NO_PERIOD_MARK_NOTIFICATION + userId, "1", 30, TimeUnit.DAYS);

        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("user:{}, no period event error", userId, ex);
            return null;
        });
    }

    private void sendNotification(AppUserInfoEntity userInfo, String timeZone,
                                  String expireDay, Long defineId) {
        messageProvider.sendNotification(new PushNotificationDTO()
                .setUserId(userInfo.getUserId())
                .setTimeZone(timeZone)
                .setSilent(Boolean.TRUE)
                .setPlatform(userInfo.getPlatform())
                .setDefineId(defineId)
                .setPushFirebase(Boolean.TRUE)
                .setSaveRecord(Boolean.TRUE)
                .setExpireTime(ZoneDateUtil.timestamp(timeZone, expireDay, DatePatternConst.DATE_TIME_PATTERN))
                .setTokens(Collections.singletonList(userInfo.getPushToken())));
    }

    private void userCompleteFirstCycle(Long userId, String today, String timeZone) {
        CompletableFuture.runAsync(() -> {
            String key = RedisCacheKeyConst.USER_FIRST_REAL_CYCLE + userId;
            String logPeriodDay = redisComponent.get(key);
            if (StringUtils.isEmpty(logPeriodDay) || LocalDateUtil.minusToDay(today, logPeriodDay) < 1) {
                return;
            }
            // if user has scheduled test today, must complete all test, then show this popup
            // if user no scheduled test today, show this popup after entering the app
            AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
            if (CollectionUtils.isEmpty(cycleDataDTOS)) {
                return;
            }
            boolean isSend = false;
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);
            Set<Integer> testWandSet = CycleDataUtil.getTestWandSet(today, currentCycleData);
            if (testWandSet.isEmpty()) {
                isSend = true;
            }
            boolean todayCompleteAllTest = CycleDataUtil.todayCompleteAllTest(today, hormoneDTOS, testWandSet);
            if (todayCompleteAllTest) {
                isSend = true;
            }
            if (isSend) {
                // send notification
                AppUserInfoEntity userInfo = appUserInfoDAO.getByUserId(userId);
                String expireDay = LocalDateUtil.plusDay(today, 2, DatePatternConst.DATE_PATTERN) + " 23:59:59";
                sendNotification(userInfo, timeZone, expireDay, NotificationDefineEnum.AFTER_1_CYCLE.getDefineId());
                redisComponent.delete(key);
            }

        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("user:{}, user complete first cycle event error", userId, ex);
            return null;
        });
    }
}
