package com.mira.user.handler.chart.hormone;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.NumberFormatUtil;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.controller.vo.cycle.TestDataVO;
import com.mira.user.dto.common.ManualHormoneDTO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * LH
 *
 * <AUTHOR>
 */
@Component
public class LhHormoneHandler extends AbstractChartHormoneHandler {
    @PostConstruct
    public void init() {
        ChartHormoneHandler.set(WandTypeEnum.LH.getInteger(), this);
    }

    @Override
    public void handle(HormoneDTO hormoneDTO, CycleDataVO cycleDataVO, ManualHormoneDTO manualHormoneDTO) {
        if (hormoneDTO != null) {
            TestDataVO lhTestDataDTO = new TestDataVO();
            lhTestDataDTO.setTestTime(hormoneDTO.getTest_time());
            lhTestDataDTO.setValue(NumberFormatUtil.format(hormoneDTO.getTest_results().getValue1()));
            cycleDataVO.getLhDatas().add(lhTestDataDTO);
        }
        if (manualHormoneDTO != null) {
            TestDataVO lhTestDataDTO = new TestDataVO();
            lhTestDataDTO.setTestTime(manualHormoneDTO.getTest_time());
            lhTestDataDTO.setValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()));
            lhTestDataDTO.setPending(1);
            cycleDataVO.getLhDatas().add(lhTestDataDTO);
        }

    }
}
