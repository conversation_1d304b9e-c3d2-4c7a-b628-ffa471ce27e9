package com.mira.user.enums.user.v5;

import lombok.Getter;

/**
 * Over the past 12 months, how regular have your periods been?
 *
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-06-11 03:07
 **/
@Getter
public enum MenopauseRegularPeriodEnum {
    Regular(0, "Regular (cycle length varies up to 7 days)"),
    Irregular(1, "Irregular (cycle length varies more than 7 days)"),
    Absence(2, "Absence of periods for more than 90 days"),
    Long_cycles(3, "Long cycles (longer than 35 days)"),
    Short_cycles(4, "Short cycles (shorter than 21 days)");

    private final Integer value;
    private final String description;

    MenopauseRegularPeriodEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }


}
