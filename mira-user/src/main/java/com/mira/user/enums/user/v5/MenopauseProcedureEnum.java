package com.mira.user.enums.user.v5;

import lombok.Getter;

/**
 * Have you had any of the following procedures?
 *
 * @program: mira_server_microservices
 * @description:
 * @author: xizhao.dai
 * @create: 2024-06-11 03:07
 **/
@Getter
public enum MenopauseProcedureEnum {
    ZERO(0, "One ovary removed"),
    ONE(1, "Both ovaries removed"),
    TWO(2, "Uterus removed"),
    THREE(3, "Endometrial ablation"),
    FOUR(4, "None of the above");

    private final Integer value;
    private final String description;

    MenopauseProcedureEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }


}
