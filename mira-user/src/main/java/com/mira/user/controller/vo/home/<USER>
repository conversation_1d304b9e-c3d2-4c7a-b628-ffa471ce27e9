package com.mira.user.controller.vo.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("首页banner")
public class HomeBannerVO {
    @ApiModelProperty("bannerId")
    private Long id;

    @ApiModelProperty("展示顺序")
    private Integer orders;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容标题")
    private String mainTitle;

    @ApiModelProperty("内容按钮")
    private String mainButtonText;

    @ApiModelProperty("文字颜色")
    private String fontColor;

    @ApiModelProperty("背景色")
    private String backgroundColor;

    @ApiModelProperty("首页banner中的图片")
    private String bannerImage;

    @ApiModelProperty("banner弹窗的主图")
    private String mainImage;

    @ApiModelProperty("跳转链接地址")
    private String link;

    @ApiModelProperty("类型：1:产品；2:blog")
    private Integer type;

    @ApiModelProperty("用户类型")
    private String userTypes;

    /**
     * see UserConditionEnum
     */
    @ApiModelProperty("健康选项")
    private Integer[] healthCondition;

    /**
     * 1:(18 - 25), 2:(26 - 30), 3:(31 - 35), 4:(36 - 40), 5:(>40)
     */
    @ApiModelProperty("年龄范围")
    private Integer[] age;

    @ApiModelProperty("图片是否覆盖整个banner，默认false:0")
    private Integer imageCover;

    @ApiModelProperty("图片padding")
    private String bannerImagePadding;

    @ApiModelProperty("是否弹窗，0不弹窗，1弹窗")
    private Integer hasModal;

    @ApiModelProperty("扩展字段")
    private String mixedString;

    @ApiModelProperty(value = "Stories")
    private Object stories;
}
