package com.mira.user.controller.thirdparty;

import com.mira.api.thirdparty.dto.xperiencify.XperiencifyDTO;
import com.mira.api.thirdparty.provider.IXperiencifyProvicer;
import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.NoPacking;
import com.mira.core.response.CommonResult;
import com.mira.core.util.JsonUtil;
import com.mira.user.exception.UserException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;

/**
 * xperiencify 平台接口
 * <p>数据格式不做处理，原封不动返回给前端</p>
 *
 * <AUTHOR>
 */
@Api(tags = "22.Xperiencify 平台")
@RestController
@RequestMapping("/app/xperiencify")
public class XperiencifyController {
    @Resource
    private IXperiencifyProvicer xperiencifyProvicer;

    @Anonymous
    @NoPacking
    @ApiOperation("获取Token")
    @PostMapping("/token")
    public Object getToken(@Valid @RequestBody XperiencifyDTO xperiencifyDTO, HttpServletResponse response) {
        CommonResult<Object> commonResult = xperiencifyProvicer.getToken(xperiencifyDTO);
        if (!"success".equals(commonResult.getMsg())) {
            String data = (String) commonResult.getData();
            String httpStatus = data.substring(0, 3);
            String errorMsg = data.substring(3);
            response.setStatus(Integer.parseInt(httpStatus));
            return JsonUtil.toObject(errorMsg, HashMap.class);
        }

        return commonResult.getData();
    }

    @Anonymous
    @NoPacking
    @ApiOperation("获取学生信息")
    @GetMapping("/student/{student_id}")
    public Object getStudent(HttpServletRequest request, HttpServletResponse response,
                             @PathVariable("student_id") String student_id,
                             @RequestParam("site_id") String site_id) {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isEmpty(authorization)) {
            throw new UserException(HttpStatus.UNAUTHORIZED.value(), "token can not be empty");
        }

        CommonResult<Object> commonResult = xperiencifyProvicer.getStudentInfo(student_id, authorization, site_id);
        if (!"success".equals(commonResult.getMsg())) {
            String data = (String) commonResult.getData();
            String httpStatus = data.substring(0, 3);
            String errorMsg = data.substring(3);
            response.setStatus(Integer.parseInt(httpStatus));
            return JsonUtil.toObject(errorMsg, HashMap.class);
        }

        return commonResult.getData();
    }

    @Anonymous
    @NoPacking
    @ApiOperation("获取所有学生信息")
    @GetMapping("/student/all")
    public Object getAllStudent(HttpServletRequest request, HttpServletResponse response,
                                @RequestParam("site_id") String site_id) {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isEmpty(authorization)) {
            throw new UserException(HttpStatus.UNAUTHORIZED.value(), "token can not be empty");
        }

        CommonResult<Object> commonResult = xperiencifyProvicer.getAllStudent(authorization, site_id);
        if (!"success".equals(commonResult.getMsg())) {
            String data = (String) commonResult.getData();
            String httpStatus = data.substring(0, 3);
            String errorMsg = data.substring(3);
            response.setStatus(Integer.parseInt(httpStatus));
            return JsonUtil.toObject(errorMsg, HashMap.class);
        }

        return commonResult.getData();
    }

    @Anonymous
    @NoPacking
    @ApiOperation("获取用户课程信息")
    @GetMapping("/user/courses")
    public Object getSuccessLoginInfo(HttpServletRequest request, HttpServletResponse response) {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isEmpty(authorization)) {
            throw new UserException(HttpStatus.UNAUTHORIZED.value(), "token can not be empty");
        }

        CommonResult<Object> commonResult = xperiencifyProvicer.getLoginInfo(authorization);
        if (!"success".equals(commonResult.getMsg())) {
            String data = (String) commonResult.getData();
            String httpStatus = data.substring(0, 3);
            String errorMsg = data.substring(3);
            response.setStatus(Integer.parseInt(httpStatus));
            return JsonUtil.toObject(errorMsg, HashMap.class);
        }

        return commonResult.getData();
    }
}
