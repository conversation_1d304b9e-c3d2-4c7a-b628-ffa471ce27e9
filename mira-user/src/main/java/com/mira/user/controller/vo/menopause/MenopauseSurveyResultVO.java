package com.mira.user.controller.vo.menopause;

import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.enums.daily.DailySymptomLevelEnum;
import com.mira.user.dto.info.v5.*;
import com.mira.user.enums.user.v5.*;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-11-21
 **/
@Getter
@Setter
@ApiModel("更年期survey result")
public class MenopauseSurveyResultVO {
    private Long id;

    /**
     * 手术
     *
     * @see MenopauseProcedureEnum
     */
    private List<Integer> procedures = new ArrayList<>();
    /**
     * 是否使用药物或者其他支持
     */
    private Integer takeMedication;
    /**
     * 同custom log中的药物
     */
    private List<UserMedicineDTO> medications = new ArrayList<>();
    /**
     * 经期规律性
     *
     * @see MenopauseRegularPeriodEnum
     */
    private List<Integer> regularPeriods = new ArrayList<>();
    /**
     * 母亲进入更年期的年龄
     * Let’s limit it from 40 to 65
     * I don't know 用 -1表示
     */
    private Integer motherEnterAge;
    /**
     * (Hormone Replacement Therapy) yes/no
     */
    private Integer hrt;
    /**
     * What kind of HRT you are on
     *
     * @see MenopauseHrtTypeEnum
     */
    private List<Integer> hrtTypes = new ArrayList<>();
    /**
     * Have you ever been pregnant?
     *
     * @see MenopausePregnantEnum
     */
    private Integer everPregnant;
    /**
     * @see MenopausePhysicallyActiveEnum
     */
    private Integer physicallyActive;
    /**
     * What type of physical activity
     *
     * @see MenopausePhysicallyActiveTypeEnum
     */
    private List<Integer> physicallyActiveTypes = new ArrayList<>();
    /**
     * 使用更年期模式的目的
     *
     * @see MenopauseGoalEnum
     */
    private List<Integer> menopauseGoals = new ArrayList<>();
    /**
     * @see MenopauseSymptomEnum perimenopausal symptoms
     * @see DailySymptomLevelEnum By default “None” is chosen for every symptom
     */
    private List<MenopauseSymptomDTO> menopauseSymptoms = new ArrayList<>();
    /**
     * 用来定位当前用户的survey完成到哪一个页面
     * 1:默认从第一页开始
     * -1:全部完成用-1表示
     *
     * @see MenopausePageCodeEnum
     */
    private Integer pageCode;
}
